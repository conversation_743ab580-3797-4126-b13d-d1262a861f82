{"$message_type":"diagnostic","message":"unused variable: `cx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":1840,"byte_end":1842,"line_start":63,"line_end":63,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":1840,"byte_end":1842,"line_start":63,"line_end":63,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":"_cx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs:63:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `cx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/usdk_utils/generic_future.rs","byte_start":863,"byte_end":865,"line_start":26,"line_end":26,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"usdk_rust/rust_usdk/src/usdk_utils/generic_future.rs","byte_start":863,"byte_end":865,"line_start":26,"line_end":26,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":"_cx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/usdk_utils/generic_future.rs:26:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `app_type_name_map` and `device_id_map` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":85,"byte_end":110,"line_start":5,"line_end":5,"column_start":12,"column_end":37,"is_primary":false,"text":[{"text":"pub struct DiscoveryDeviceNameHelper {","highlight_start":12,"highlight_end":37}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":117,"byte_end":134,"line_start":6,"line_end":6,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    app_type_name_map: HashMap<String, HashMap<i32, bool>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":177,"byte_end":190,"line_start":7,"line_end":7,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    device_id_map: HashMap<String, i32>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`DiscoveryDeviceNameHelper` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `app_type_name_map` and `device_id_map` are never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DiscoveryDeviceNameHelper {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_type_name_map: HashMap<String, HashMap<i32, bool>>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    device_id_map: HashMap<String, i32>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `DiscoveryDeviceNameHelper` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `get_device_index`, `get_device_name`, and `clear_device_name` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":217,"byte_end":247,"line_start":10,"line_end":10,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"impl DiscoveryDeviceNameHelper {","highlight_start":1,"highlight_end":31}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":261,"byte_end":264,"line_start":11,"line_end":11,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":431,"byte_end":447,"line_start":18,"line_end":18,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn get_device_index(&mut self, device_id: &str, app_type_name: &str) -> i32 {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":1641,"byte_end":1656,"line_start":53,"line_end":53,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_device_name(&mut self, device_id: &str, app_type_name: &str) -> String {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":2040,"byte_end":2057,"line_start":63,"line_end":63,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn clear_device_name(&mut self, device_id: &str, app_type_name: &str) {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `get_device_index`, `get_device_name`, and `clear_device_name` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs:11:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DiscoveryDeviceNameHelper {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_index(&mut self, device_id: &str, app_type_name: &str) -> i32 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_name(&mut self, device_id: &str, app_type_name: &str) -> String {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn clear_device_name(&mut self, device_id: &str, app_type_name: &str) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `C_ERR_ENUM_UNDEFINED` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":508,"byte_end":528,"line_start":17,"line_end":17,"column_start":7,"column_end":27,"is_primary":true,"text":[{"text":"const C_ERR_ENUM_UNDEFINED: i32 = 20001;","highlight_start":7,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `C_ERR_ENUM_UNDEFINED` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs:17:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst C_ERR_ENUM_UNDEFINED: i32 = 20001;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `SUCCESS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":52,"byte_end":59,"line_start":2,"line_end":2,"column_start":22,"column_end":29,"is_primary":true,"text":[{"text":"    pub(crate) const SUCCESS: i32 = 0;","highlight_start":22,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `SUCCESS` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:2:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const SUCCESS: i32 = 0;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RUST_OTHER_ERROR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":126,"byte_end":142,"line_start":5,"line_end":5,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"    pub(crate) const RUST_OTHER_ERROR: i32 = 108;","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RUST_OTHER_ERROR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:5:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const RUST_OTHER_ERROR: i32 = 108;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `SERDE_JSON_ERR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":181,"byte_end":195,"line_start":7,"line_end":7,"column_start":22,"column_end":36,"is_primary":true,"text":[{"text":"    pub(crate) const SERDE_JSON_ERR: i32 = 109;","highlight_start":22,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `SERDE_JSON_ERR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:7:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const SERDE_JSON_ERR: i32 = 109;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `DEVICE_ERR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":229,"byte_end":239,"line_start":8,"line_end":8,"column_start":22,"column_end":32,"is_primary":true,"text":[{"text":"    pub(crate) const DEVICE_ERR: i32 = 110;","highlight_start":22,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `DEVICE_ERR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:8:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const DEVICE_ERR: i32 = 110;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `UHSD_BIND_DEFAULT_TIMEOUT` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":320,"byte_end":345,"line_start":12,"line_end":12,"column_start":15,"column_end":40,"is_primary":true,"text":[{"text":"    pub const UHSD_BIND_DEFAULT_TIMEOUT: u16 = 30;","highlight_start":15,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `UHSD_BIND_DEFAULT_TIMEOUT` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:12:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const UHSD_BIND_DEFAULT_TIMEOUT: u16 = 30;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"10 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 10 warnings emitted\u001b[0m\n\n"}
