; ModuleID = 'autocfg_43927d988923fad3_1.781aa390c91e37b6-cgu.0'
source_filename = "autocfg_43927d988923fad3_1.781aa390c91e37b6-cgu.0"
target datalayout = "e-m:o-i64:64-i128:128-n32:64-S128-Fn32"
target triple = "arm64-apple-macosx11.0.0"

@alloc_f93507f8ba4b5780b14b2c2584609be0 = private unnamed_addr constant <{ [8 x i8] }> <{ [8 x i8] c"\00\00\00\00\00\00\F0?" }>, align 8
@alloc_ef0a1f828f3393ef691f2705e817091c = private unnamed_addr constant <{ [8 x i8] }> <{ [8 x i8] c"\00\00\00\00\00\00\00@" }>, align 8
@__llvm_coverage_mapping = private constant { { i32, i32, i32, i32 }, [102 x i8] } { { i32, i32, i32, i32 } { i32 0, i32 102, i32 0, i32 6 }, [102 x i8] c"\02bcx\DA\05\C11\0E\830\0C\05\D0\8D\DB\C4\0E\0CQ\91*n\C1\D2-\18C\AD\8AD\FDN\06z\FA\BE\F7\E2\D5\15\CE\BFw\FFd\FB\1A\93d\9C\95\A1\A7y\C3\CD\0E\E1\CB\80\0A\A7\EEMH\F7NRB\1A\F5H\1Ae\8F\E9\D8\E6\ED\C1\A5_\A1![\F3\10i\A2q\1E\9E\B9\D4\B2\FC\01\EBI\22\04" }, section "__LLVM_COV,__llvm_covmap", align 8
@__covrec_597A3B55DAF7128Eu = linkonce_odr hidden constant <{ i64, i32, i64, i64, [9 x i8] }> <{ i64 6447531056466170510, i32 9, i64 812610001354512787, i64 -1946645055770587735, [9 x i8] c"\01\01\00\01\01\01\01\002" }>, section "__LLVM_COV,__llvm_covfun", align 8
@__llvm_profile_runtime = external hidden global i32
@__profc__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe = private global [1 x i64] zeroinitializer, section "__DATA,__llvm_prf_cnts", align 8
@__profd__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe = private global { i64, i64, i64, i64, ptr, ptr, i32, [3 x i16], i32 } { i64 6447531056466170510, i64 812610001354512787, i64 sub (i64 ptrtoint (ptr @__profc__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe to i64), i64 ptrtoint (ptr @__profd__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe to i64)), i64 0, ptr null, ptr null, i32 1, [3 x i16] zeroinitializer, i32 0 }, section "__DATA,__llvm_prf_data,regular,live_support", align 8
@__llvm_prf_nm = private constant [62 x i8] c"4<x\DA\8B\0F\F2+s.N\CC\CA\CA\F07\8E\0A\A84-\8D72K,-\C9ONK\8F71\B642O\B1\B4\B0\B042NKL1\8E74-(\CAOJ\05\00\D1\AA\11\16", section "__DATA,__llvm_prf_names", align 1
@llvm.compiler.used = appending global [2 x ptr] [ptr @__llvm_profile_runtime_user, ptr @__profd__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe], section "llvm.metadata"
@llvm.used = appending global [3 x ptr] [ptr @__llvm_coverage_mapping, ptr @__covrec_597A3B55DAF7128Eu, ptr @__llvm_prf_nm], section "llvm.metadata"
@__llvm_profile_filename = weak hidden constant [22 x i8] c"default_%m_%p.profraw\00"

; <f64>::total_cmp
; Function Attrs: inlinehint uwtable
define internal i8 @_RNvMNtCslxZGBWDpKxU_4core3f64d9total_cmpCsajjhO3ZPy5u_26autocfg_43927d988923fad3_1(ptr align 8 %self, ptr align 8 %other) unnamed_addr #0 {
start:
  %right = alloca [8 x i8], align 8
  %left = alloca [8 x i8], align 8
  %self1 = load double, ptr %self, align 8
  %_4 = bitcast double %self1 to i64
  store i64 %_4, ptr %left, align 8
  %self2 = load double, ptr %other, align 8
  %_7 = bitcast double %self2 to i64
  store i64 %_7, ptr %right, align 8
  %_13 = load i64, ptr %left, align 8
  %_12 = ashr i64 %_13, 63
  %_10 = lshr i64 %_12, 1
  %0 = load i64, ptr %left, align 8
  %1 = xor i64 %0, %_10
  store i64 %1, ptr %left, align 8
  %_18 = load i64, ptr %right, align 8
  %_17 = ashr i64 %_18, 63
  %_15 = lshr i64 %_17, 1
  %2 = load i64, ptr %right, align 8
  %3 = xor i64 %2, %_15
  store i64 %3, ptr %right, align 8
  %_21 = load i64, ptr %left, align 8
  %_22 = load i64, ptr %right, align 8
  %4 = icmp sgt i64 %_21, %_22
  %5 = zext i1 %4 to i8
  %6 = icmp slt i64 %_21, %_22
  %7 = zext i1 %6 to i8
  %_0 = sub nsw i8 %5, %7
  ret i8 %_0
}

; autocfg_43927d988923fad3_1::probe
; Function Attrs: uwtable
define void @_RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe() unnamed_addr #1 {
start:
  %0 = atomicrmw add ptr @__profc__RNvCsajjhO3ZPy5u_26autocfg_43927d988923fad3_15probe, i64 1 monotonic, align 8
; call <f64>::total_cmp
  %_1 = call i8 @_RNvMNtCslxZGBWDpKxU_4core3f64d9total_cmpCsajjhO3ZPy5u_26autocfg_43927d988923fad3_1(ptr align 8 @alloc_f93507f8ba4b5780b14b2c2584609be0, ptr align 8 @alloc_ef0a1f828f3393ef691f2705e817091c)
  ret void
}

; Function Attrs: nounwind
declare void @llvm.instrprof.increment(ptr, i64, i32, i32) #2

; Function Attrs: noinline
define linkonce_odr hidden i32 @__llvm_profile_runtime_user() #3 {
  %1 = load i32, ptr @__llvm_profile_runtime, align 4
  ret i32 %1
}

attributes #0 = { inlinehint uwtable "frame-pointer"="non-leaf" "probe-stack"="inline-asm" "target-cpu"="apple-m1" }
attributes #1 = { uwtable "frame-pointer"="non-leaf" "probe-stack"="inline-asm" "target-cpu"="apple-m1" }
attributes #2 = { nounwind }
attributes #3 = { noinline }

!llvm.module.flags = !{!0}
!llvm.ident = !{!1}

!0 = !{i32 8, !"PIC Level", i32 2}
!1 = !{!"rustc version 1.82.0 (f6e511eec 2024-10-15)"}
