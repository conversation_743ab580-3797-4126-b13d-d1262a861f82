{"rustc": 4013192585442940105, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13708040221295731214, "profile": 14201519139735489027, "path": 12197724942227325127, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-f80e0d8762c2c178/dep-build-script-build-script-build"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 3120428256180033778, "config": 2202906307356721367, "compile_kind": 0}