{"rustc": 4013192585442940105, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 14098227409853078778, "profile": 15821721394985614323, "path": 9166269512238007978, "deps": [[461436706529125561, "futures_io", false, 977292735094938289], [554324495028472449, "memchr", false, 9563422226991850251], [1910231660504989506, "futures_task", false, 8211506753936885967], [4761952582670444189, "pin_utils", false, 15180810049092039806], [5846781562065118163, "futures_channel", false, 9240666684773805331], [9396302785578940539, "futures_core", false, 2049364681874293601], [10080452282735337284, "futures_macro", false, 15678727746773808592], [11289432439818403777, "futures_sink", false, 12791082212748633397], [11809678037142197677, "pin_project_lite", false, 6843537608454717085], [17040352472033410869, "slab", false, 1044909755292366201]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-a221e6240a7838ea/dep-lib-futures_util"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 5677230335954518303, "config": 2202906307356721367, "compile_kind": 0}