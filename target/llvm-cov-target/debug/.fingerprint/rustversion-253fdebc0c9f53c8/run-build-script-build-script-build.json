{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2324944722148377429, "build_script_build", false, 4038595031951277215]], "local": [{"RerunIfChanged": {"output": "debug/build/rustversion-253fdebc0c9f53c8/output", "paths": ["build/build.rs"]}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}