{"rustc": 4013192585442940105, "features": "[\"alloc\", \"default\", \"fs\", \"std\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 9652763411108993936, "profile": 4848699998500576361, "path": 1473072997754688003, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-cd8f06e211d48f10/dep-build-script-build-script-build"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}