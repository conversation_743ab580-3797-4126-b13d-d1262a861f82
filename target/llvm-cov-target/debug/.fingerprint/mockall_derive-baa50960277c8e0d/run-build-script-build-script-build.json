{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9357895591565833906, "build_script_build", false, 13557873903507511605]], "local": [{"RerunIfChanged": {"output": "debug/build/mockall_derive-baa50960277c8e0d/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}