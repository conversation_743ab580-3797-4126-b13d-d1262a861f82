{"rustc": 4013192585442940105, "features": "[\"ahash\", \"inline-more\", \"raw\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 8830771204028428646, "profile": 6609184196851301694, "path": 6300245214736647026, "deps": [[5487915632734539349, "ahash", false, 15529028898862226164]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-301bb16f0ab3fdec/dep-lib-hashbrown"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}