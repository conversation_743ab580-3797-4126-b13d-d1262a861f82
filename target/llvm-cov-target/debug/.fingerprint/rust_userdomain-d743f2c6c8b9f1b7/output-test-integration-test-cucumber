{"$message_type":"diagnostic","message":"unused import: `QUERY_FAMILY_LIST`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_family_data_source.rs","byte_start":1787,"byte_end":1804,"line_start":33,"line_end":33,"column_start":82,"column_end":99,"is_primary":true,"text":[{"text":"    MOVE_DEVICES_TO_OTHER_FAMILY, MOVE_DEVICES_TO_OTHER_ROOM, QUERY_FAMILY_INFO, QUERY_FAMILY_LIST,","highlight_start":82,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_family_data_source.rs","byte_start":1785,"byte_end":1804,"line_start":33,"line_end":33,"column_start":80,"column_end":99,"is_primary":true,"text":[{"text":"    MOVE_DEVICES_TO_OTHER_FAMILY, MOVE_DEVICES_TO_OTHER_ROOM, QUERY_FAMILY_INFO, QUERY_FAMILY_LIST,","highlight_start":80,"highlight_end":99}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `QUERY_FAMILY_LIST`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/fake/data_source/fake_family_data_source.rs:33:82\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    MOVE_DEVICES_TO_OTHER_FAMILY, MOVE_DEVICES_TO_OTHER_ROOM, QUERY_FAMILY_INFO, QUERY_FAMILY_LIST,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Mutex`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/cache_steps.rs","byte_start":4,"byte_end":20,"line_start":1,"line_end":1,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use std::sync::Mutex;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/cache_steps.rs","byte_start":0,"byte_end":22,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Mutex;","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::sync::Mutex`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/cache_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Mutex;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":4,"byte_end":15,"line_start":1,"line_end":1,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":0,"byte_end":17,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `task_manager::task_manager::TaskError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":86,"byte_end":123,"line_start":5,"line_end":5,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":82,"byte_end":125,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":1,"highlight_end":43},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `task_manager::task_manager::TaskError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse task_manager::task_manager::TaskError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Result` and `UserDomainError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":374,"byte_end":380,"line_start":12,"line_end":12,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":382,"byte_end":397,"line_start":12,"line_end":12,"column_start":43,"column_end":58,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":43,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":340,"byte_end":400,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":1,"highlight_end":60},{"text":"use rust_userdomain::api::user_domain_manager::UserDomainManager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Result` and `UserDomainError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:12:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::error::{Result, UserDomainError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `get_error_from_retcode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":586,"byte_end":608,"line_start":17,"line_end":17,"column_start":33,"column_end":55,"is_primary":true,"text":[{"text":"    get_device_infos_from_step, get_error_from_retcode, get_group_device_list_result_from_step,","highlight_start":33,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":584,"byte_end":608,"line_start":17,"line_end":17,"column_start":31,"column_end":55,"is_primary":true,"text":[{"text":"    get_device_infos_from_step, get_error_from_retcode, get_group_device_list_result_from_step,","highlight_start":31,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `get_error_from_retcode`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:17:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    get_device_infos_from_step, get_error_from_retcode, get_group_device_list_result_from_step,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cucumber::gherkin::Step`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":4,"byte_end":27,"line_start":1,"line_end":1,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":0,"byte_end":29,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":29},{"text":"use cucumber::{given, then};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `cucumber::gherkin::Step`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/event_step.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::gherkin::Step;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `given`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":44,"byte_end":49,"line_start":2,"line_end":2,"column_start":16,"column_end":21,"is_primary":true,"text":[{"text":"use cucumber::{given, then};","highlight_start":16,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":44,"byte_end":51,"line_start":2,"line_end":2,"column_start":16,"column_end":23,"is_primary":true,"text":[{"text":"use cucumber::{given, then};","highlight_start":16,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":43,"byte_end":44,"line_start":2,"line_end":2,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use cucumber::{given, then};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/event_step.rs","byte_start":55,"byte_end":56,"line_start":2,"line_end":2,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::{given, then};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `given`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/event_step.rs:2:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{given, then};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `sync::Mutex` and `thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":32,"byte_end":43,"line_start":1,"line_end":1,"column_start":33,"column_end":44,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Mutex, thread};","highlight_start":33,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":45,"byte_end":51,"line_start":1,"line_end":1,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Mutex, thread};","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":30,"byte_end":51,"line_start":1,"line_end":1,"column_start":31,"column_end":52,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Mutex, thread};","highlight_start":31,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":9,"byte_end":10,"line_start":1,"line_end":1,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Mutex, thread};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":51,"byte_end":52,"line_start":1,"line_end":1,"column_start":52,"column_end":53,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Mutex, thread};","highlight_start":52,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `sync::Mutex` and `thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:1:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::HashMap, sync::Mutex, thread};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `task_manager::task_manager::TaskError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":109,"byte_end":146,"line_start":4,"line_end":4,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":105,"byte_end":148,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":1,"highlight_end":43},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `task_manager::task_manager::TaskError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse task_manager::task_manager::TaskError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":398,"byte_end":404,"line_start":10,"line_end":10,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":398,"byte_end":406,"line_start":10,"line_end":10,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":397,"byte_end":398,"line_start":10,"line_end":10,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":421,"byte_end":422,"line_start":10,"line_end":10,"column_start":58,"column_end":59,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":58,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:10:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::error::{Result, UserDomainError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DeviceAggregationArgs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":602,"byte_end":623,"line_start":14,"line_end":14,"column_start":22,"column_end":43,"is_primary":true,"text":[{"text":"    AggCardItemArgs, DeviceAggregationArgs, DeviceCardStatusArgs,","highlight_start":22,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":600,"byte_end":623,"line_start":14,"line_end":14,"column_start":20,"column_end":43,"is_primary":true,"text":[{"text":"    AggCardItemArgs, DeviceAggregationArgs, DeviceCardStatusArgs,","highlight_start":20,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DeviceAggregationArgs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:14:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AggCardItemArgs, DeviceAggregationArgs, DeviceCardStatusArgs,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::models::device_info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":654,"byte_end":690,"line_start":16,"line_end":16,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"use rust_userdomain::models::device_info;","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":650,"byte_end":692,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::models::device_info;","highlight_start":1,"highlight_end":42},{"text":"use rust_userdomain::models::family_args::FamilyArgs;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::models::device_info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::models::device_info;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `get_error_from_retcode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":1099,"byte_end":1121,"line_start":25,"line_end":25,"column_start":77,"column_end":99,"is_primary":true,"text":[{"text":"    assets_family_info, create_family_location, get_device_infos_from_step, get_error_from_retcode,","highlight_start":77,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":1097,"byte_end":1121,"line_start":25,"line_end":25,"column_start":75,"column_end":99,"is_primary":true,"text":[{"text":"    assets_family_info, create_family_location, get_device_infos_from_step, get_error_from_retcode,","highlight_start":75,"highlight_end":99}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `get_error_from_retcode`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:25:77\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    assets_family_info, create_family_location, get_device_infos_from_step, get_error_from_retcode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":4,"byte_end":15,"line_start":1,"line_end":1,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":0,"byte_end":17,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":87,"byte_end":92,"line_start":5,"line_end":5,"column_start":16,"column_end":21,"is_primary":true,"text":[{"text":"use cucumber::{event, given, then, when};","highlight_start":16,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":87,"byte_end":94,"line_start":5,"line_end":5,"column_start":16,"column_end":23,"is_primary":true,"text":[{"text":"use cucumber::{event, given, then, when};","highlight_start":16,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `event`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:5:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{event, given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `task_manager::task_manager::TaskError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":118,"byte_end":155,"line_start":6,"line_end":6,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":114,"byte_end":157,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":1,"highlight_end":43},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `task_manager::task_manager::TaskError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse task_manager::task_manager::TaskError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `TASK_QUERY_FAMILY_INFO` and `TASK_QUERY_FAMILY_LIST`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":224,"byte_end":246,"line_start":9,"line_end":9,"column_start":18,"column_end":40,"is_primary":true,"text":[{"text":"    TASK_LOGOUT, TASK_QUERY_FAMILY_INFO, TASK_QUERY_FAMILY_LIST, TASK_REFRESH_USER_INFO,","highlight_start":18,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":248,"byte_end":270,"line_start":9,"line_end":9,"column_start":42,"column_end":64,"is_primary":true,"text":[{"text":"    TASK_LOGOUT, TASK_QUERY_FAMILY_INFO, TASK_QUERY_FAMILY_LIST, TASK_REFRESH_USER_INFO,","highlight_start":42,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":222,"byte_end":270,"line_start":9,"line_end":9,"column_start":16,"column_end":64,"is_primary":true,"text":[{"text":"    TASK_LOGOUT, TASK_QUERY_FAMILY_INFO, TASK_QUERY_FAMILY_LIST, TASK_REFRESH_USER_INFO,","highlight_start":16,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `TASK_QUERY_FAMILY_INFO` and `TASK_QUERY_FAMILY_LIST`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:9:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TASK_LOGOUT, TASK_QUERY_FAMILY_INFO, TASK_QUERY_FAMILY_LIST, TASK_REFRESH_USER_INFO,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::api::error::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":303,"byte_end":338,"line_start":11,"line_end":11,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::Result;","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":299,"byte_end":340,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::Result;","highlight_start":1,"highlight_end":41},{"text":"use rust_userdomain::api::error::UserDomainError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::api::error::Result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::error::Result;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::api::error::UserDomainError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":344,"byte_end":388,"line_start":12,"line_end":12,"column_start":5,"column_end":49,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::UserDomainError;","highlight_start":5,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":340,"byte_end":390,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::UserDomainError;","highlight_start":1,"highlight_end":50},{"text":"use rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::api::error::UserDomainError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::error::UserDomainError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":394,"byte_end":458,"line_start":13,"line_end":13,"column_start":5,"column_end":69,"is_primary":true,"text":[{"text":"use rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin;","highlight_start":5,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":390,"byte_end":460,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin;","highlight_start":1,"highlight_end":70},{"text":"use rust_userdomain::api::user_domain::UserDomain;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::event::UserDomainEvent::MessageCancelLogin;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::api::user_domain::UserDomain`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":464,"byte_end":509,"line_start":14,"line_end":14,"column_start":5,"column_end":50,"is_primary":true,"text":[{"text":"use rust_userdomain::api::user_domain::UserDomain;","highlight_start":5,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":460,"byte_end":511,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::api::user_domain::UserDomain;","highlight_start":1,"highlight_end":51},{"text":"use rust_userdomain::api::user_domain_manager::UserDomainManager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::api::user_domain::UserDomain`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::user_domain::UserDomain;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_userdomain::tools::extension_string::OptionStringExtension`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":721,"byte_end":784,"line_start":19,"line_end":19,"column_start":5,"column_end":68,"is_primary":true,"text":[{"text":"use rust_userdomain::tools::extension_string::OptionStringExtension;","highlight_start":5,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":717,"byte_end":786,"line_start":19,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_userdomain::tools::extension_string::OptionStringExtension;","highlight_start":1,"highlight_end":69},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_userdomain::tools::extension_string::OptionStringExtension`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::tools::extension_string::OptionStringExtension;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `QUERY_FAMILY_LIST` and `QUERY_USER_INFO`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":910,"byte_end":927,"line_start":22,"line_end":22,"column_start":48,"column_end":65,"is_primary":true,"text":[{"text":"use crate::utils::user_domain_holder::action::{QUERY_FAMILY_LIST, QUERY_USER_INFO};","highlight_start":48,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":929,"byte_end":944,"line_start":22,"line_end":22,"column_start":67,"column_end":82,"is_primary":true,"text":[{"text":"use crate::utils::user_domain_holder::action::{QUERY_FAMILY_LIST, QUERY_USER_INFO};","highlight_start":67,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs","byte_start":863,"byte_end":947,"line_start":22,"line_end":23,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::utils::user_domain_holder::action::{QUERY_FAMILY_LIST, QUERY_USER_INFO};","highlight_start":1,"highlight_end":84},{"text":"use crate::utils::user_domain_holder::{DataHolder, UserDomainHolder};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `QUERY_FAMILY_LIST` and `QUERY_USER_INFO`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_domain_steps.rs:22:48\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::user_domain_holder::action::{QUERY_FAMILY_LIST, QUERY_USER_INFO};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":4,"byte_end":14,"line_start":1,"line_end":1,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use serde_json;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":0,"byte_end":16,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json;","highlight_start":1,"highlight_end":16},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `serde_json`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":57,"byte_end":63,"line_start":3,"line_end":3,"column_start":11,"column_end":17,"is_primary":true,"text":[{"text":"use std::{thread, vec};","highlight_start":11,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":57,"byte_end":65,"line_start":3,"line_end":3,"column_start":11,"column_end":19,"is_primary":true,"text":[{"text":"use std::{thread, vec};","highlight_start":11,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":56,"byte_end":57,"line_start":3,"line_end":3,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{thread, vec};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":68,"byte_end":69,"line_start":3,"line_end":3,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use std::{thread, vec};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:3:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{thread, vec};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `task_manager::task_manager::TaskError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":188,"byte_end":225,"line_start":8,"line_end":8,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":184,"byte_end":227,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use task_manager::task_manager::TaskError;","highlight_start":1,"highlight_end":43},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `task_manager::task_manager::TaskError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse task_manager::task_manager::TaskError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":902,"byte_end":908,"line_start":20,"line_end":20,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":35,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":902,"byte_end":910,"line_start":20,"line_end":20,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":901,"byte_end":902,"line_start":20,"line_end":20,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":925,"byte_end":926,"line_start":20,"line_end":20,"column_start":58,"column_end":59,"is_primary":true,"text":[{"text":"use rust_userdomain::api::error::{Result, UserDomainError};","highlight_start":58,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:20:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_userdomain::api::error::{Result, UserDomainError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `QUERY_USER_INFO`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":1837,"byte_end":1852,"line_start":36,"line_end":36,"column_start":79,"column_end":94,"is_primary":true,"text":[{"text":"    CANCEL_DEVICE_SHARING, CONFIRM_DEVICE_SHARING, MODIFY_AGGREGATION_SWITCH, QUERY_USER_INFO,","highlight_start":79,"highlight_end":94}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":1835,"byte_end":1852,"line_start":36,"line_end":36,"column_start":77,"column_end":94,"is_primary":true,"text":[{"text":"    CANCEL_DEVICE_SHARING, CONFIRM_DEVICE_SHARING, MODIFY_AGGREGATION_SWITCH, QUERY_USER_INFO,","highlight_start":77,"highlight_end":94}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `QUERY_USER_INFO`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:36:79\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CANCEL_DEVICE_SHARING, CONFIRM_DEVICE_SHARING, MODIFY_AGGREGATION_SWITCH, QUERY_USER_INFO,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":8872,"byte_end":8882,"line_start":210,"line_end":210,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"        Some(mut family) => {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":8872,"byte_end":8876,"line_start":210,"line_end":210,"column_start":14,"column_end":18,"is_primary":true,"text":[{"text":"        Some(mut family) => {","highlight_start":14,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:210:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(mut family) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `thread_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs","byte_start":512,"byte_end":523,"line_start":12,"line_end":12,"column_start":53,"column_end":64,"is_primary":true,"text":[{"text":"async fn thread_refresh_token(_world: &mut MyWorld, thread_name: String) {","highlight_start":53,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs","byte_start":512,"byte_end":523,"line_start":12,"line_end":12,"column_start":53,"column_end":64,"is_primary":true,"text":[{"text":"async fn thread_refresh_token(_world: &mut MyWorld, thread_name: String) {","highlight_start":53,"highlight_end":64}],"label":null,"suggested_replacement":"_thread_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `thread_name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs:12:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn thread_refresh_token(_world: &mut MyWorld, thread_name: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_thread_name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs","byte_start":1010,"byte_end":1019,"line_start":27,"line_end":27,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"async fn user_refresh_token(_world: &mut MyWorld, user_name: String) {","highlight_start":51,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs","byte_start":1010,"byte_end":1019,"line_start":27,"line_end":27,"column_start":51,"column_end":60,"is_primary":true,"text":[{"text":"async fn user_refresh_token(_world: &mut MyWorld, user_name: String) {","highlight_start":51,"highlight_end":60}],"label":null,"suggested_replacement":"_user_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/refresh_token_steps.rs:27:51\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn user_refresh_token(_world: &mut MyWorld, user_name: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":6962,"byte_end":6975,"line_start":159,"line_end":159,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":31}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this will be a hard error in the 2024 edition","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `addr_of_mut!` instead to create a raw pointer","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":6962,"byte_end":6967,"line_start":159,"line_end":159,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":"addr_of_mut!(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":6975,"byte_end":6975,"line_start":159,"line_end":159,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:159:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        unsafe { &mut INSTANCE }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this will be a hard error in the 2024 edition\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `addr_of_mut!` instead to create a raw pointer\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        unsafe { \u001b[0m\u001b[0m\u001b[38;5;10maddr_of_mut!(\u001b[0m\u001b[0mINSTANCE\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `refresh_token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs","byte_start":5310,"byte_end":5323,"line_start":125,"line_end":125,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    async fn refresh_token(&self, refresh_token: &str) -> Result<AuthData, UserDomainError> {","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs","byte_start":5310,"byte_end":5323,"line_start":125,"line_end":125,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    async fn refresh_token(&self, refresh_token: &str) -> Result<AuthData, UserDomainError> {","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":"_refresh_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `refresh_token`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs:125:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn refresh_token(&self, refresh_token: &str) -> Result<AuthData, UserDomainError> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_refresh_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs","byte_start":6437,"byte_end":6438,"line_start":145,"line_end":145,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    Err(e) => Err(get_result_error(result)),","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs","byte_start":6437,"byte_end":6438,"line_start":145,"line_end":145,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    Err(e) => Err(get_result_error(result)),","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/fake/data_source/fake_user_data_source_impl.rs:145:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Err(e) => Err(get_result_error(result)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ok`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":6460,"byte_end":6462,"line_start":173,"line_end":173,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    let ok = parse_call_result(result_str.as_str());","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":6460,"byte_end":6462,"line_start":173,"line_end":173,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    let ok = parse_call_result(result_str.as_str());","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":"_ok","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ok`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:173:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let ok = parse_call_result(result_str.as_str());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ok`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `family_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":12557,"byte_end":12566,"line_start":342,"line_end":342,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"fn group_device_list_data(_world: &mut MyWorld, family_id: String, step: &Step) {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/device_steps.rs","byte_start":12557,"byte_end":12566,"line_start":342,"line_end":342,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"fn group_device_list_data(_world: &mut MyWorld, family_id: String, step: &Step) {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":"_family_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `family_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/device_steps.rs:342:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn group_device_list_data(_world: &mut MyWorld, family_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_family_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ok`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":24271,"byte_end":24273,"line_start":597,"line_end":597,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    let ok = parse_call_result(result_str.as_str());","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/family_steps.rs","byte_start":24271,"byte_end":24273,"line_start":597,"line_end":597,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    let ok = parse_call_result(result_str.as_str());","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":"_ok","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ok`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/family_steps.rs:597:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m597\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let ok = parse_call_result(result_str.as_str());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ok`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":20851,"byte_end":20856,"line_start":487,"line_end":487,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"fn verify_create_address_count_params(world: &mut MyWorld, count: String, address: String) {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/steps/user_steps.rs","byte_start":20851,"byte_end":20856,"line_start":487,"line_end":487,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"fn verify_create_address_count_params(world: &mut MyWorld, count: String, address: String) {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/steps/user_steps.rs:487:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m487\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn verify_create_address_count_params(world: &mut MyWorld, count: String, address: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `Int`, `Float`, `ModifyUserInfo`, `DoubleString`, `UnbindDevicesBody`, and `MoveDevicesToOtherRoomBody` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":4995,"byte_end":5006,"line_start":103,"line_end":103,"column_start":10,"column_end":21,"is_primary":false,"text":[{"text":"pub enum ParamHolder {","highlight_start":10,"highlight_end":21}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5013,"byte_end":5016,"line_start":104,"line_end":104,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    Int(Vec<i32>),","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5032,"byte_end":5037,"line_start":105,"line_end":105,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    Float(Vec<f32>),","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5078,"byte_end":5092,"line_start":107,"line_end":107,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    ModifyUserInfo(String),","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5422,"byte_end":5434,"line_start":116,"line_end":116,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    DoubleString((String, String)),","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5583,"byte_end":5600,"line_start":120,"line_end":120,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    UnbindDevicesBody(UnbindDevicesBody),","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5707,"byte_end":5733,"line_start":124,"line_end":124,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    MoveDevicesToOtherRoomBody(MoveDevicesToOtherRoomBody),","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ParamHolder` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variants `Int`, `Float`, `ModifyUserInfo`, `DoubleString`, `UnbindDevicesBody`, and `MoveDevicesToOtherRoomBody` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:104:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ParamHolder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Int(Vec<i32>),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Float(Vec<f32>),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    String(Vec<String>),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ModifyUserInfo(String),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    DoubleString((String, String)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UnbindDevicesBody(UnbindDevicesBody),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    MoveDevicesToOtherRoomBody(MoveDevicesToOtherRoomBody),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ParamHolder` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"enum `MockResultHolder` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":5884,"byte_end":5900,"line_start":130,"line_end":130,"column_start":10,"column_end":26,"is_primary":true,"text":[{"text":"pub enum MockResultHolder {","highlight_start":10,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MockResultHolder` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: enum `MockResultHolder` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:130:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum MockResultHolder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `MockResultHolder` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `get_member_info_vec` and `get_event_list` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":6872,"byte_end":6893,"line_start":157,"line_end":157,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl UserDomainHolder {","highlight_start":1,"highlight_end":22}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":9317,"byte_end":9336,"line_start":231,"line_end":231,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn get_member_info_vec(&self) -> Vec<FamilyMemberInfo> {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":13324,"byte_end":13338,"line_start":351,"line_end":351,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn get_event_list(&mut self) -> &mut Vec<String> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `get_member_info_vec` and `get_event_list` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:231:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl UserDomainHolder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_member_info_vec(&self) -> Vec<FamilyMemberInfo> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_event_list(&mut self) -> &mut Vec<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `QUERY_USER_INFO` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":1929,"byte_end":1944,"line_start":40,"line_end":40,"column_start":15,"column_end":30,"is_primary":true,"text":[{"text":"    pub const QUERY_USER_INFO: &str = \"query_user_info\";","highlight_start":15,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `QUERY_USER_INFO` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:40:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const QUERY_USER_INFO: &str = \"query_user_info\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `QUERY_FAMILY_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":1986,"byte_end":2003,"line_start":41,"line_end":41,"column_start":15,"column_end":32,"is_primary":true,"text":[{"text":"    pub const QUERY_FAMILY_LIST: &str = \"query_family_list\";","highlight_start":15,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `QUERY_FAMILY_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:41:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const QUERY_FAMILY_LIST: &str = \"query_family_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `MODIFY_USER_INFO` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"userdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs","byte_start":2388,"byte_end":2404,"line_start":47,"line_end":47,"column_start":15,"column_end":31,"is_primary":true,"text":[{"text":"    pub const MODIFY_USER_INFO: &str = \"modify_user_info\";","highlight_start":15,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `MODIFY_USER_INFO` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muserdomain_rust/rust_userdomain/tests/utils/user_domain_holder.rs:47:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const MODIFY_USER_INFO: &str = \"modify_user_info\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"45 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 45 warnings emitted\u001b[0m\n\n"}
