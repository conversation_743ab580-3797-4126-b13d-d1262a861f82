{"rustc": 4013192585442940105, "features": "[\"alloc\", \"event\", \"fs\", \"libc-extra-traits\", \"net\", \"pipe\", \"process\", \"std\", \"time\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 9652763411108993936, "profile": 11619710921444552153, "path": 6190028999272814007, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-16771538cf106498/dep-build-script-build-script-build"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}