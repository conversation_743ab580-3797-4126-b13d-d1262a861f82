{"rustc": 4013192585442940105, "features": "[\"kv\", \"kv_unstable\", \"std\", \"value-bag\"]", "declared_features": "[\"kv\", \"kv_serde\", \"kv_std\", \"kv_sval\", \"kv_unstable\", \"kv_unstable_serde\", \"kv_unstable_std\", \"kv_unstable_sval\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"serde\", \"std\", \"sval\", \"sval_ref\", \"value-bag\"]", "target": 1712620997370137394, "profile": 6609184196851301694, "path": 4738583287360443538, "deps": [[2987908753095373714, "value_bag", false, 4969605272952712178]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/log-b8aa490785b19b1c/dep-lib-log"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 179143468214550567, "config": 2202906307356721367, "compile_kind": 0}