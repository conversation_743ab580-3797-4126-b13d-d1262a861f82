{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18196723110576764154, "build_script_build", false, 11102049944845395456]], "local": [{"RerunIfChanged": {"output": "debug/build/zerocopy-fecc4f01f210f27a/output", "paths": ["build.rs", "Cargo.toml"]}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}