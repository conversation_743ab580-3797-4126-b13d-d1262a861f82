{"rustc": 4013192585442940105, "features": "[\"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"arbitrary\", \"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 4336297695352704405, "profile": 6609184196851301694, "path": 11878915311193315329, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-syntax-3b2cad7d2fc430b6/dep-lib-regex_syntax"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 17586400164587752172, "config": 2202906307356721367, "compile_kind": 0}