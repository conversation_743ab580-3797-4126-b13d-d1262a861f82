{"rustc": 4013192585442940105, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[128838192112201762, "build_script_build", false, 8805101074241509359]], "local": [{"RerunIfChanged": {"output": "debug/build/parking_lot_core-e97d79388b4676c5/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 0, "config": 0, "compile_kind": 0}