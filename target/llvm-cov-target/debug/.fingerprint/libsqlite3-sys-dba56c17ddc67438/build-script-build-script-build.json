{"rustc": 4013192585442940105, "features": "[\"bundled_bindings\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 9652763411108993936, "profile": 6707562999592697545, "path": 49143928098405690, "deps": [[7218239883571173546, "pkg_config", false, 3717503827711313483], [8444855962349136217, "vcpkg", false, 9902727155863245916]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsqlite3-sys-dba56c17ddc67438/dep-build-script-build-script-build"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 14430067081211154946, "config": 2202906307356721367, "compile_kind": 0}