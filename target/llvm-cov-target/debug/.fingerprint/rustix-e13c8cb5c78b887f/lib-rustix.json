{"rustc": 4013192585442940105, "features": "[\"alloc\", \"default\", \"fs\", \"std\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 1236213820636157529, "profile": 13834304442285559527, "path": 1264495812686358706, "deps": [[1167534579069247419, "build_script_build", false, 4540629065084158244], [5912081054555210979, "libc_errno", false, 4963816148661130704], [7404036440948165371, "bitflags", false, 4417017909147225861], [7762067171913260472, "libc", false, 16119834969248363409]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-e13c8cb5c78b887f/dep-lib-rustix"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}