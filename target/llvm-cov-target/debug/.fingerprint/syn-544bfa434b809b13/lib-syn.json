{"rustc": 4013192585442940105, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 14790166133050072309, "profile": 6707562999592697545, "path": 367255615055851972, "deps": [[7006636483571730090, "unicode_ident", false, 6368084523236935368], [17525013869477438691, "quote", false, 15766748181239020803], [18036439996138669183, "proc_macro2", false, 3181049191361358098]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-544bfa434b809b13/dep-lib-syn"}}], "rustflags": ["-C", "instrument-coverage", "--cfg=coverage", "--cfg=trybuild_no_target"], "metadata": 6886477143387768027, "config": 2202906307356721367, "compile_kind": 0}