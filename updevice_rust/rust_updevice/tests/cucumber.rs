mod fake;
mod steps;
mod utils;

use std::time::Duration;

use crate::utils::device_test_holder::UpDeviceTestHolder;
use cucumber::World;
use futures::FutureExt;
use tokio::time;

#[derive(Debug, Default, World)]
pub struct MyWorld {}

impl MyWorld {
    pub fn new() -> Self {
        MyWorld {}
    }
}

#[tokio::main]
async fn main() {
    std::env::set_var("CARGO_TEST", "1");
    MyWorld::cucumber()
        .max_concurrent_scenarios(1)
        .before(|_feature, _rule, _scenario, _world| {
            time::sleep(Duration::from_millis(1)).boxed_local()
        })
        .after(|_feature, _gherkin, _rule, _scenario, _world| {
            UpDeviceTestHolder::get_instance().clear();
            time::sleep(Duration::from_millis(1)).boxed_local()
        })
        // .filter_run("tests/features", |_, _, sc| {
        //     sc.tags.iter().any(|t| t == "debug")
        // })
        .run_and_exit("tests/features/deviceModels.feature")
        .await;
}
