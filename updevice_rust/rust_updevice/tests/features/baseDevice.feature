Feature: 设备基类逻辑
  功能范围:
  UpDevice中的所有设备类均是设备基类的子类，它们需要遵循的共同业务逻辑均体现在设备基类中，具体如下：
  - 设备相关操作下发到对应的Toolkit，具体包括查询、订阅设备状态、子类列表，执行设备各种操作
  - 支持多个订阅者，设备的状态(报警、属性、子设备列表、extra信息、基本属性、连接)变化后，会通知订阅者
  - 会缓存设备状态变化，当设备状态变化查询设备状态会查询到最新的设备状态信息
  - prepare后会自动向Toolkit下发查询当前的报警、属性、子设备的命令，以确保准备完成后是设备的最新状态
  - 当设置ui进程report开关后，将在UI线程通知订阅者

  外部依赖:
  1.Toolkit
  2.线程调度器
  3.设备工厂

  接口说明:
  1.订阅设备状态
  支持多个订阅者，当usdk上报设备的状态变化时，会通知订阅者，
  监听的状态包括设备基础信息变化，设备连接状态，子设备列表变化，属性列表变化，报警列表变化，fota状态变化，蓝牙数据和蓝牙历史数据变化。

  2.取消订阅设备状态
  取消订阅后，设备状态变化不会发送给已经取消的订阅者。

  3.订阅设备资源
  支持多个订阅者，当usdk上报设备的图片资源时，会通知所有订阅者。

  4.取消订阅设备资源
  取消订阅后，设备图片资源上报不会发送给已经取消的订阅者。

  Background:
    Given 初始化设备管理器
    Given 使用者创建"测试"设备工厂
    Given 创建"测试"设备,唯一标识ID为"uniqueId",设备信息如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             | Extra                                                                                                                                                                                                                                                                                                                                                                                                             |
      | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 | {"DI-Basic.displayName":"fake_name","DI-Basic.room":"fake_room","DI-Basic.roomId":"fake_room_id","DI-Basic.bindTime":"fake_bind_time","DI-Product.brand":"fake_brand","DI-Product.bind_type":"fake_bind_type","DI-Product.configType":"fake_configT_type","DI-Relation.ownerId":"fake_owner_id","DI-Product.imageAddr1":"fake_image_addr1","DI-Permission.authType":"fake_auth_type","DI-Permission.edit":"true"} |

  Scenario: [1000]多个用户订阅了设备，设备属性状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化，具体属性变化列表如下
      | name  | value |
      | attrA | 1     |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_ATTRIBUTES_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_ATTRIBUTES_CHANGE |
    Then 使用者查询设备"fake_device_id1"属性列表如下
      | name  | value |
      | attrA | 1     |

  Scenario: [1001]多个用户订阅了设备，设备报警状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体报警变化列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action               |
      | EVENT_DEVICE_CAUTION |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action               |
      | EVENT_DEVICE_CAUTION |
    Then 使用者查询设备"fake_device_id1"报警列表如下
      | name     | value | time  |
      | cautionA | 1     | 86400 |

  Scenario: [1002]多个用户订阅了设备，设备连接状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"状态发生变化,具体连接状态变为"CONNECTING"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_CONNECTION_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                  |
      | EVENT_CONNECTION_CHANGE |
    Then 使用者查询设备"fake_device_id1"连接状态为"CONNECTING"

  Scenario: [1003]多个用户订阅了设备，设备连接状态变化后，多个用户均收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"离在线状态发生变化,具体状态变为"ONLINE"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_ONLINE_STATE_CHANGE |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_ONLINE_STATE_CHANGE |
    Then 使用者查询设备"fake_device_id1"离在线状态为"ONLINE"

  Scenario: [1003]多个用户订阅了设备，某个用户解除了订阅，设备连接状态变化后，解除订阅的用户不会收到通知
    When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
    When 订阅者"userA"取消订阅设备"fake_device_id1"的设备状态
    When 设备"fake_device_id1"离在线状态发生变化,具体状态变为"ONLINE"
    Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
      | Action |
    Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
      | Action                    |
      | EVENT_ONLINE_STATE_CHANGE |
    Then 使用者查询设备"fake_device_id1"离在线状态为"ONLINE"

  # Scenario: [1003]多个用户订阅了设备，设备就绪状态变化后，如果没有子设备，不会收到子设备列表变化通知，只发送连接状态改变通知，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                  |
  #     | EVENT_CONNECTION_CHANGE |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                  |
  #     | EVENT_CONNECTION_CHANGE |
  #   Then 使用者查询设备"fake_device_id1"连接状态为"READY"

  # Scenario: [1004]多个用户订阅了设备，设备就绪状态变化后，如果有子设备，会收到子设备列表变化通知和连接状态改变通知，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 使用者查询设备"fake_device_id1"连接状态为"READY"
  #   Then 使用者查询设备"fake_device_id1"子设备列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  # @ios_ignore
  # Scenario: [1005]多个用户订阅了设备，设备子类列表状态变化后，多个用户均收到通知，Android子设备列表不关心连接状态
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #   Then 使用者查询设备"fake_device_id1"子设备列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  # @android_ignore
  # Scenario: [1005-2]多个用户订阅了设备，设备子类列表状态变化后，多个用户均收到通知，iOS只有当连接状态UpDeviceConnection_READY时返回子设备列表
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体连接状态变为"READY"
  #   When 设备"fake_device_id1"状态发生变化,具体子设备变化列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 使用者查询设备"fake_device_id1"连接状态为"READY"
  #   Then 使用者查询设备"fake_device_id1"子设备列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |

  # Scenario: [1006]多个用户订阅了设备，设备基本信息变化后，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体设备基本信息变化为
  #     | Protocol   | DeviceId        | TypeId        | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name11 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                 |
  #     | EVENT_BASE_INFO_CHANGE |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                 |
  #     | EVENT_BASE_INFO_CHANGE |
  #   Then 使用者查询设备"fake_device_id1"基本信息如下
  #     | Protocol   | DeviceId        | TypeId        | TypeName         | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name11 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |

  # Scenario: [1007]多个用户订阅了设备，设备Fota状态变化后，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"的Fota状态发生变化
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                          |
  #     | EVENT_DEVICE_FOTA_STATUS_CHANGE |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                          |
  #     | EVENT_DEVICE_FOTA_STATUS_CHANGE |

  # Scenario: [1008]多个用户订阅了设备，设备蓝牙数据变化后，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体蓝牙数据变化为"1"
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                         |
  #     | EVENT_DEVICE_BLE_REALTIME_DATA |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                         |
  #     | EVENT_DEVICE_BLE_REALTIME_DATA |
  #   Then 使用者查询设备"fake_device_id1"蓝牙数据为"1"

  # Scenario: [1009]多个用户订阅了设备，设备蓝牙历史数据变化后，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 设备"fake_device_id1"状态发生变化,具体蓝牙历史变化如下
  #     | currentCount | totalCount | data |
  #     | 1            | 2          | 3    |
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                        |
  #     | EVENT_DEVICE_BLE_HISTORY_DATA |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                        |
  #     | EVENT_DEVICE_BLE_HISTORY_DATA |
  #   Then 使用者查询设备"fake_device_id1"蓝牙历史数据为
  #     | currentCount | totalCount | data |
  #     | 1            | 2          | 3    |

  # Scenario: [1010]多个用户订阅了设备资源，设备资源上传后，多个用户均收到通知
  #   When 订阅者"userA"订阅设备"fake_device_id1"的数据
  #   When 订阅者"userB"订阅设备"fake_device_id1"的数据
  #   When 设备"fake_device_id1"状态发生变化,具体资源数据如下
  #     | name  | value |
  #     | nameA | 1     |
  #   Then 订阅者"userA"收到设备"fake_device_id1"资源如下
  #     | name  | value |
  #     | nameA | 1     |
  #   Then 订阅者"userB"收到设备"fake_device_id1"资源如下
  #     | name  | value |
  #     | nameA | 1     |

  # @ios_ignore
  # Scenario: [1011]设备prepare成功后，会立即发送一次当前设备最新状态通知，iOS与Android Notify的事件有差异，Android子设备列表不关心连接状态
  #   Given Toolkit的连接设备接口的执行结果为"成功"
  #   Given Toolkit的获取设备连接状态返回"OFFLINE"
  #   Given Toolkit的获取设备基础信息返回数据如下
  #     | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
  #   Given Toolkit的获取设备属性列表返回数据如下
  #     | name  | value |
  #     | attrA | 1     |
  #   Given Toolkit的获取设备报警列表返回数据如下
  #     | name     | value | time  |
  #     | cautionA | 1     | 86400 |
  #   Given Toolkit的获取子设备列表返回数据如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 使用者调用设备"fake_device_id1"的准备接口
  #   When 等待"1"秒
  #   Then 使用者收到设备"fake_device_id1"的准备接口的结果为"成功"
  #   Then Toolkit的获取设备基础信息接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备连接状态接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取子设备列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备属性列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备报警列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_BASE_INFO_CHANGE    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_ATTRIBUTES_CHANGE   |
  #     | EVENT_DEVICE_CAUTION      |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_BASE_INFO_CHANGE    |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_CONNECTION_CHANGE   |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_ATTRIBUTES_CHANGE   |
  #     | EVENT_DEVICE_CAUTION      |
  #   Then 使用者查询设备"fake_device_id1"属性列表如下
  #     | name  | value |
  #     | attrA | 1     |
  #   Then 使用者查询设备"fake_device_id1"报警列表如下
  #     | name     | value | time  |
  #     | cautionA | 1     | 86400 |
  #   Then 使用者查询设备"fake_device_id1"连接状态为"OFFLINE"
  #   Then 使用者查询设备"fake_device_id1"子设备列表如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   Then 使用者查询设备"fake_device_id1"基本信息如下
  #     | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |

  # @android_ignore
  # Scenario: [1011-2]设备prepare成功后，会立即发送一次当前设备最新状态通知，iOS与Android Notify的事件有差异，iOS只有当连接状态UpDeviceConnection_READY时返回子设备列表
  #   Given Toolkit的连接设备接口的执行结果为"成功"
  #   Given Toolkit的获取设备连接状态返回"OFFLINE"
  #   Given Toolkit的获取设备基础信息返回数据如下
  #     | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |
  #   Given Toolkit的获取设备属性列表返回数据如下
  #     | name  | value |
  #     | attrA | 1     |
  #   Given Toolkit的获取设备报警列表返回数据如下
  #     | name     | value | time  |
  #     | cautionA | 1     | 86400 |
  #   Given Toolkit的获取子设备列表返回数据如下
  #     | Protocol   | DeviceId             | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        |
  #     | haier-usdk | fake_device_id18-8-1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 |
  #   When 订阅者"userA"订阅设备"fake_device_id1"的设备状态
  #   When 订阅者"userB"订阅设备"fake_device_id1"的设备状态
  #   When 使用者调用设备"fake_device_id1"的准备接口
  #   When 等待"1"秒
  #   Then 使用者收到设备"fake_device_id1"的准备接口的结果为"成功"
  #   Then Toolkit的获取设备基础信息接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备连接状态接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取子设备列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备属性列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then Toolkit的获取设备报警列表接口调用"1"次,设备id为"fake_device_id1"
  #   Then 订阅者"userA"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_BASE_INFO_CHANGE    |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_ATTRIBUTES_CHANGE   |
  #     | EVENT_DEVICE_CAUTION      |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 订阅者"userB"收到设备"fake_device_id1"的事件如下
  #     | Action                    |
  #     | EVENT_BASE_INFO_CHANGE    |
  #     | EVENT_DEVICE_STATE_CHANGE |
  #     | EVENT_SUB_DEV_LIST_CHANGE |
  #     | EVENT_ATTRIBUTES_CHANGE   |
  #     | EVENT_DEVICE_CAUTION      |
  #     | EVENT_CONNECTION_CHANGE   |
  #   Then 使用者查询设备"fake_device_id1"属性列表如下
  #     | name  | value |
  #     | attrA | 1     |
  #   Then 使用者查询设备"fake_device_id1"报警列表如下
  #     | name     | value | time  |
  #     | cautionA | 1     | 86400 |
  #   Then 使用者查询设备"fake_device_id1"连接状态为"OFFLINE"
  #   Then 使用者查询设备"fake_device_id1"子设备列表如下
  #     | Protocol | DeviceId | TypeId | TypeName | TypeCode | Model | ProdNo | ParentId | SubDevNo |
  #   Then 使用者查询设备"fake_device_id1"基本信息如下
  #     | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo             |
  #     | haier-usdk | fake_device_id1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id18-8-1 |

  Scenario: [1012]ffi设备属性模型转换成UpDevice的设备属性模型后值相同
    Then ffi设备属性数据转换UpDevice的设备属性数据后值的数据对比相同:
      | name  | value |
      | attrA | 1     |

  Scenario: [1012-1]ffi设备属性模型列表转换成UpDevice的设备属性模型列表后值相同
    Then ffi设备属性列表数据转换UpDevice的设备属性列表数据后值的数据对比相同:
      | name  | value |
      | attrA | 1     |
      | attrB | 2     |
      | attrC | 3     |

  Scenario: [1013]UpDeviceBasic的赋的值，与获取的属性值相同
    Then UpDeviceBasic的获取属性值相同,期望值为:
      | display_name | app_type_name | app_type_code | room_name | room_id      | online      | device_role | device_role_type | device_net_type | device_group_id | device_group_type | bind_time      |
      | fake_name    |               |               | fake_room | fake_room_id | fake_online |             |                  |                 |                 |                   | fake_bind_time |

  Scenario: [1013-1]当UpDeviceBasic的赋的值为空，与获取的属性值相同
    Then UpDeviceBasic为empty时，获取属性值也为空

  Scenario: [1014]UpDevicePermission的赋的值，与获取的属性值相同
    Then UpDevicePermission的获取属性值相同,期望值为:
      | auth_type      | is_controllable | is_editable | is_viewable |
      | fake_auth_type |                 | true        |             |

  Scenario: [1014-1]当UpDevicePermission的赋的值为空，与获取的属性值相同
    Then UpDevicePermission为empty时，获取属性值也为空

  Scenario: [1015]UpDeviceProduct的赋的值，与获取的属性值相同
    Then UpDeviceProduct的获取属性值相同,期望值为:
      | bar_code | brand      | category | category_code | category_grouping | device_type     | image_url        | bind_type      | access_type | communication_mode | config_type       | app_type_icon | no_keep_alive |
      |          | fake_brand |          |               |                   | fake_type_code1 | fake_image_addr1 | fake_bind_type |             |                    | fake_configT_type |               |               |

  Scenario: [1015-1]当UpDeviceProduct的赋的值为空，与获取的属性值相同
    Then UpDeviceProduct为empty时，获取属性值也为空

  Scenario: [1016]UpDeviceRelation的赋的值，与获取的属性值相同
    Then UpDeviceRelation的获取属性值相同,期望值为:
      | owner_id      | owner_phone | family_id | uc_user_id | floor_id | floor_name | floor_order_id |
      | fake_owner_id |             |           |            |          |            |                |

  Scenario: [1016-1]当UpDeviceRelation的赋的值为空，与获取的属性值相同
    Then UpDeviceRelation为empty时，获取属性值也为空

  Scenario: [1017]ffi设备属性模型列表转换成UpDeviceCaution模型列表后值相同
    Then ffi设备属性列表数据转换UpDeviceCaution模型列表数据后值的数据对比相同:
      | name  | value |
      | attrA | 1     |
      | attrB | 2     |
      | attrC | 3     |

  # 设备类型测试用例
  Scenario: [1018]创建聚合设备并验证基本功能,基本信息和功能正常
    Given 创建"聚合"设备,唯一标识ID为"aggregate_unique_id",设备信息如下:
      | Protocol   | DeviceId           | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                          |
      | haier-usdk | aggregate_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"聚合设备","DI-Basic.room":"客厅","DI-Basic.roomId":"room_001","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"aggregate","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 聚合设备"aggregate_unique_id"的设备ID为"aggregate_device_1"
    Then 聚合设备"aggregate_unique_id"的协议为"haier-usdk"
    Then 聚合设备"aggregate_unique_id"的扩展API不为空

  Scenario: [1019]创建通用设备并验证基本功能,基本信息和功能正常
    Given 创建"通用"设备,唯一标识ID为"common_unique_id",设备信息如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                       |
      | haier-usdk | common_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"通用设备","DI-Basic.room":"卧室","DI-Basic.roomId":"room_002","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"common","DI-Product.category":"家电","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 通用设备"common_unique_id"的设备ID为"common_device_1"
    Then 通用设备"common_unique_id"的协议为"haier-usdk"
    Then 通用设备"common_unique_id"的扩展API不为空

  Scenario: [1020]创建洗衣机设备并验证基本功能,基本信息和功能正常
    Given 创建"洗衣机"设备,唯一标识ID为"washing_unique_id",设备信息如下:
      | Protocol   | DeviceId         | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                                                                                                                                                                                                                        |
      | haier-usdk | washing_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"洗衣机","DI-Basic.room":"阳台","DI-Basic.roomId":"room_003","DI-Basic.bindTime":"2024-01-01","DI-Product.brand":"海尔","DI-Product.bind_type":"wifi","DI-Product.configType":"washing","DI-Product.category":"洗衣机","DI-Relation.ownerId":"owner_001","DI-Product.imageAddr1":"http://image.url"} |
    Then 洗衣机设备"washing_unique_id"的设备ID为"washing_device_1"
    Then 洗衣机设备"washing_unique_id"的协议为"haier-usdk"
    Then 洗衣机设备"washing_unique_id"的扩展API不为空

  # 设备过滤器测试用例
  Scenario: [1021]根据家庭ID过滤设备列表,返回设备列表中包含家庭设备
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                                                                                                        |
      | haier-usdk | family_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Relation.familyId":"family_001","DI-Basic.displayName":"家庭设备1","DI-Basic.room":"客厅","DI-Basic.roomId":"room_001"} |
      | haier-usdk | family_device_2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Relation.familyId":"family_002","DI-Basic.displayName":"家庭设备2","DI-Basic.room":"卧室","DI-Basic.roomId":"room_002"} |
      | haier-usdk | family_device_3 | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id4 | {"DI-Relation.familyId":"family_001","DI-Basic.displayName":"家庭设备3","DI-Basic.room":"厨房","DI-Basic.roomId":"room_003"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 创建家庭ID过滤器,家庭ID为"family_001"
    Then 使用家庭ID过滤器过滤设备列表,返回的设备数量为"2"
    Then 使用家庭ID过滤器过滤设备列表,返回的设备ID包含"family_device_1,family_device_3"

  Scenario: [1022]根据设备ID过滤设备列表,返回设备列表中包含目标设备
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                |
      | haier-usdk | target_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 | fake_parent_id1 | fake_device_id2 | {"DI-Basic.displayName":"目标设备1"} |
      | haier-usdk | target_device_2 | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | fake_parent_id2 | fake_device_id3 | {"DI-Basic.displayName":"目标设备2"} |
      | haier-usdk | other_device_1  | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | fake_parent_id3 | fake_device_id4 | {"DI-Basic.displayName":"其他设备1"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 创建设备ID列表过滤器,设备ID列表为"target_device_1,target_device_2"
    Then 使用设备ID列表过滤器过滤设备列表,返回的设备数量为"2"
    Then 使用设备ID列表过滤器过滤设备列表,返回的设备ID包含"target_device_1,target_device_2"

  Scenario: [1023]根据存在的父设备ID过滤设备列表,返回设备列表中包含目标设备
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId        | SubDevNo        | Extra                                 |
      | haier-usdk | parent_device_1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 |                 | fake_device_id2 | {"DI-Basic.displayName":"父设备1"}    |
      | haier-usdk | child_device_1  | fake_type_id2 | fake_type_name2 | fake_type_code2 | fake_model2 | fake_pro_no2 | parent_device_1 | fake_device_id3 | {"DI-Basic.displayName":"子设备1"}    |
      | haier-usdk | child_device_2  | fake_type_id3 | fake_type_name3 | fake_type_code3 | fake_model3 | fake_pro_no3 | parent_device_1 | fake_device_id4 | {"DI-Basic.displayName":"子设备2"}    |
      | haier-usdk | other_child_1   | fake_type_id4 | fake_type_name4 | fake_type_code4 | fake_model4 | fake_pro_no4 | other_parent_1  | fake_device_id5 | {"DI-Basic.displayName":"其他子设备"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 创建父设备ID过滤器,父设备ID为"parent_device_1"
    Then 使用父设备ID过滤器过滤设备列表,返回的设备数量为"2"
    Then 使用父设备ID过滤器过滤设备列表,返回的设备ID包含"child_device_1,child_device_2"

  Scenario: [1024]根据不存在的父设备ID过滤设备列表,返回设备列表中不包含目标设备
    Given 设备数据源的更新设备列表接口返回数据如下:
      | Protocol   | DeviceId        | TypeId        | TypeName        | TypeCode        | Model       | ProdNo       | ParentId | SubDevNo        | Extra                                |
      | haier-usdk | standalone_dev1 | fake_type_id1 | fake_type_name1 | fake_type_code1 | fake_model1 | fake_pro_no1 |          | fake_device_id2 | {"DI-Basic.displayName":"独立设备1"} |
    When 用户更新设备列表,立即更新标志为"FALSE"
    When 等待"1"秒
    Given 创建父设备ID过滤器,父设备ID为"nonexistent_parent"
    Then 使用父设备ID过滤器过滤设备列表,返回的设备数量为"0"