Feature: 其他功能测试

  Background:
    Given 初始化设备管理器
    Given 使用者创建"测试"设备工厂


  # 用户数据源测试
  Scenario: [3001]用户数据源订阅用户信息变化，订阅者收到登出事件
    Given 用户数据源初始化完成
    When 订阅者"userB"订阅用户信息变化
    When userdomain上报退出登录事件
    Then 订阅者"userB"收到用户信息变化事件"Logout"

  Scenario: [3002]用户数据源取消订阅测试，订阅者收不到事件
    Given 用户数据源初始化完成
    When 订阅者"userC"订阅用户信息变化
    When 订阅者"userC"取消订阅用户信息变化
    When userdomain上报退出登录事件
    Then 订阅者"userC"未收到用户信息变化事件


  # 事件通道测试
  Scenario: [3008]获取设备变化事件通道，通道不为空
    When 获取设备变化事件通道
    Then 设备变化事件通道不为空

  Scenario: [3009]设备列表变化事件通道，通道不为空
    When 获取设备列表变化事件通道
    Then 设备列表变化事件通道不为空

