<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../style.css'><script src='../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/logic_engine_rust/rust_logicEngine/src/device_config/constraint.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use super::deserialize_rules::{</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    deserialize_any_to_string, deserialize_string_int_or_float, deserialize_string_or_int,</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    deserialize_string_to_bool,</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>};</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device_config::deserialize_rules::Validator;</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::de::{Deserializer, Visitor};</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::{Deserialize, Serialize};</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::collections::HashMap;</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::fmt;</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize)]</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct Constraint {</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub pending_condition: PendingCondition,</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub additional_commands: AdditionalCommands,</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub report_value: Option&lt;ReportValue&gt;,</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Constraint {</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.pending_condition.is_valid()</span></pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; <span class='region red'>self.additional_commands.is_valid()</span></pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; <span class='region red'>self.report_value.as_ref().map_or(true, </span>|c| <span class='region red'>c.is_valid()</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for Constraint {</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct ConstraintVisitor;</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for ConstraintVisitor {</pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = Constraint;</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct Splitter&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut pending_condition = PendingCondition::default();</span></pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut additional_commands = AdditionalCommands::default();</span></pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut report_value = None</span>;</pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;pendingCondition&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            <span class='region red'>pending_condition = map</span></pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                .next_value()</span></pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                .unwrap_or_else(</span>|_| <span class='region red'>PendingCondition::default()</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;additionalCommands&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            <span class='region red'>additional_commands = map</span></pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                .next_value()</span></pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                .unwrap_or_else(</span>|_| <span class='region red'>AdditionalCommands::default()</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;reportValue&quot;</span> =&gt; <span class='region red'>report_value = map.next_value().unwrap_or(None)</span>,</pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(Constraint {</span></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    pending_condition,</span></pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    additional_commands,</span></pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    report_value,</span></pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                })</span></pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(ConstraintVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Default)]</pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct PendingCondition {</pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub operator: Operator,</pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub commands: Option&lt;HashMap&lt;String, Vec&lt;String&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for PendingCondition {</pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !String::from(self.operator).eq(&quot;NONE&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for PendingCondition {</pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct PendingConditionVisitor;</pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for PendingConditionVisitor {</pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = PendingCondition;</pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct PendingCondition&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut operator = Operator::None;</span></pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut commands = None</span>;</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;operator&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>op: Option&lt;String&gt; = map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>op</span>) = <span class='region red'>op</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                operator = Operator::from(op);</span></pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;commands&quot;</span> =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            commands = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        }</span></pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(PendingCondition { operator, commands })</span></pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(PendingConditionVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Deserialize, Default, Clone, Copy)]</pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;UPPERCASE&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum Operator {</pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[default]</pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    None,</pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    And,</pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Or,</pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl From&lt;Operator&gt; for String {</pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn from(value: Operator) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match value</span> {</pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Operator::Or =&gt; <span class='region red'>&quot;OR&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Operator::And =&gt; <span class='region red'>&quot;AND&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Operator::None =&gt; <span class='region red'>&quot;NONE&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl From&lt;String&gt; for Operator {</pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn from(value: String) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match value.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &quot;OR&quot;</span> =&gt; <span class='region red'>Operator::Or</span>,</pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>&quot;AND&quot;</span> =&gt; <span class='region red'>Operator::And</span>,</pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            _ =&gt; <span class='region red'>Operator::None</span>,</pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Default)]</pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct AdditionalCommands {</pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub merge_type: MergeType,</pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub commands: Vec&lt;Command&gt;,</pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for AdditionalCommands {</pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !String::from(self.merge_type).eq(&quot;NONE&quot;)</span> &amp;&amp; <span class='region red'>!self.commands.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Deserialize, Default, Clone, Copy)]</pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;UPPERCASE&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum MergeType {</pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[default]</pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    None,</pre></td></tr><tr><td class='line-number'><a name='L179' href='#L179'><pre>179</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Replace,</pre></td></tr><tr><td class='line-number'><a name='L180' href='#L180'><pre>180</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Append,</pre></td></tr><tr><td class='line-number'><a name='L181' href='#L181'><pre>181</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Prepend,</pre></td></tr><tr><td class='line-number'><a name='L182' href='#L182'><pre>182</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L183' href='#L183'><pre>183</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl From&lt;MergeType&gt; for String {</pre></td></tr><tr><td class='line-number'><a name='L184' href='#L184'><pre>184</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn from(value: MergeType) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L185' href='#L185'><pre>185</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match value</span> {</pre></td></tr><tr><td class='line-number'><a name='L186' href='#L186'><pre>186</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            MergeType::Append =&gt; <span class='region red'>&quot;APPEND&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L187' href='#L187'><pre>187</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            MergeType::Prepend =&gt; <span class='region red'>&quot;PREPEND&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L188' href='#L188'><pre>188</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            MergeType::Replace =&gt; <span class='region red'>&quot;REPLACE&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L189' href='#L189'><pre>189</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            MergeType::None =&gt; <span class='region red'>&quot;NONE&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L190' href='#L190'><pre>190</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L191' href='#L191'><pre>191</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L192' href='#L192'><pre>192</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L193' href='#L193'><pre>193</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for AdditionalCommands {</pre></td></tr><tr><td class='line-number'><a name='L194' href='#L194'><pre>194</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L195' href='#L195'><pre>195</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L196' href='#L196'><pre>196</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L197' href='#L197'><pre>197</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L198' href='#L198'><pre>198</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct AdditionalCommandsVisitor;</pre></td></tr><tr><td class='line-number'><a name='L199' href='#L199'><pre>199</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L200' href='#L200'><pre>200</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for AdditionalCommandsVisitor {</pre></td></tr><tr><td class='line-number'><a name='L201' href='#L201'><pre>201</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = AdditionalCommands;</pre></td></tr><tr><td class='line-number'><a name='L202' href='#L202'><pre>202</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L203' href='#L203'><pre>203</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L204' href='#L204'><pre>204</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct AdditionalCommands&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L205' href='#L205'><pre>205</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L206' href='#L206'><pre>206</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L207' href='#L207'><pre>207</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L208' href='#L208'><pre>208</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L209' href='#L209'><pre>209</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L210' href='#L210'><pre>210</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L211' href='#L211'><pre>211</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut merge_type = MergeType::None;</span></pre></td></tr><tr><td class='line-number'><a name='L212' href='#L212'><pre>212</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut commands = Vec::new()</span>;</pre></td></tr><tr><td class='line-number'><a name='L213' href='#L213'><pre>213</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L214' href='#L214'><pre>214</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L215' href='#L215'><pre>215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L216' href='#L216'><pre>216</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;mergeType&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L217' href='#L217'><pre>217</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>mt: Option&lt;MergeType&gt; = map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L218' href='#L218'><pre>218</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>mt</span>) = <span class='region red'>mt</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L219' href='#L219'><pre>219</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                merge_type = mt;</span></pre></td></tr><tr><td class='line-number'><a name='L220' href='#L220'><pre>220</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L221' href='#L221'><pre>221</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L222' href='#L222'><pre>222</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;commands&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L223' href='#L223'><pre>223</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>command_list: Option&lt;Vec&lt;Command&gt;&gt; =</span></pre></td></tr><tr><td class='line-number'><a name='L224' href='#L224'><pre>224</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L225' href='#L225'><pre>225</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>command_list</span>) = <span class='region red'>command_list</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L226' href='#L226'><pre>226</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                commands =</span></pre></td></tr><tr><td class='line-number'><a name='L227' href='#L227'><pre>227</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                    command_list.into_iter().filter(Command::is_valid).collect();</span></pre></td></tr><tr><td class='line-number'><a name='L228' href='#L228'><pre>228</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L229' href='#L229'><pre>229</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L230' href='#L230'><pre>230</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L231' href='#L231'><pre>231</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L232' href='#L232'><pre>232</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L233' href='#L233'><pre>233</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L234' href='#L234'><pre>234</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L235' href='#L235'><pre>235</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(AdditionalCommands {</span></pre></td></tr><tr><td class='line-number'><a name='L236' href='#L236'><pre>236</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    merge_type,</span></pre></td></tr><tr><td class='line-number'><a name='L237' href='#L237'><pre>237</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    commands,</span></pre></td></tr><tr><td class='line-number'><a name='L238' href='#L238'><pre>238</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                })</span></pre></td></tr><tr><td class='line-number'><a name='L239' href='#L239'><pre>239</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L240' href='#L240'><pre>240</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L241' href='#L241'><pre>241</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L242' href='#L242'><pre>242</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(AdditionalCommandsVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L243' href='#L243'><pre>243</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L244' href='#L244'><pre>244</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L245' href='#L245'><pre>245</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L246' href='#L246'><pre>246</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>#[derive(Debug, Serialize, <span class='region red'>Deserialize</span>)]</pre></td></tr><tr><td class='line-number'><a name='L247' href='#L247'><pre>247</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct Command {</pre></td></tr><tr><td class='line-number'><a name='L248' href='#L248'><pre>248</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub name: String,</pre></td></tr><tr><td class='line-number'><a name='L249' href='#L249'><pre>249</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_any_to_string&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L250' href='#L250'><pre>250</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub value: String,</pre></td></tr><tr><td class='line-number'><a name='L251' href='#L251'><pre>251</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L252' href='#L252'><pre>252</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Command {</pre></td></tr><tr><td class='line-number'><a name='L253' href='#L253'><pre>253</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L254' href='#L254'><pre>254</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !self.name.is_empty()</span> &amp;&amp; <span class='region red'>!self.value.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L255' href='#L255'><pre>255</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L256' href='#L256'><pre>256</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L257' href='#L257'><pre>257</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize)]</pre></td></tr><tr><td class='line-number'><a name='L258' href='#L258'><pre>258</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ReportValue {</pre></td></tr><tr><td class='line-number'><a name='L259' href='#L259'><pre>259</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub relation: Operator,</pre></td></tr><tr><td class='line-number'><a name='L260' href='#L260'><pre>260</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub property: Vec&lt;Property&gt;,</pre></td></tr><tr><td class='line-number'><a name='L261' href='#L261'><pre>261</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L262' href='#L262'><pre>262</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for ReportValue {</pre></td></tr><tr><td class='line-number'><a name='L263' href='#L263'><pre>263</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L264' href='#L264'><pre>264</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !String::from(self.relation).eq(&quot;NONE&quot;)</span> &amp;&amp; <span class='region red'>!self.property.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L265' href='#L265'><pre>265</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L266' href='#L266'><pre>266</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L267' href='#L267'><pre>267</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for ReportValue {</pre></td></tr><tr><td class='line-number'><a name='L268' href='#L268'><pre>268</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L269' href='#L269'><pre>269</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L270' href='#L270'><pre>270</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L271' href='#L271'><pre>271</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L272' href='#L272'><pre>272</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct ReportValueVisitor;</pre></td></tr><tr><td class='line-number'><a name='L273' href='#L273'><pre>273</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L274' href='#L274'><pre>274</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for ReportValueVisitor {</pre></td></tr><tr><td class='line-number'><a name='L275' href='#L275'><pre>275</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = ReportValue;</pre></td></tr><tr><td class='line-number'><a name='L276' href='#L276'><pre>276</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L277' href='#L277'><pre>277</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L278' href='#L278'><pre>278</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct ReportValue&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L279' href='#L279'><pre>279</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L280' href='#L280'><pre>280</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L281' href='#L281'><pre>281</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L282' href='#L282'><pre>282</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L283' href='#L283'><pre>283</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L284' href='#L284'><pre>284</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L285' href='#L285'><pre>285</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut relation = Operator::None;</span></pre></td></tr><tr><td class='line-number'><a name='L286' href='#L286'><pre>286</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut property = Vec::new()</span>;</pre></td></tr><tr><td class='line-number'><a name='L287' href='#L287'><pre>287</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L288' href='#L288'><pre>288</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L289' href='#L289'><pre>289</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L290' href='#L290'><pre>290</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;relation&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L291' href='#L291'><pre>291</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>op: Option&lt;String&gt; = map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L292' href='#L292'><pre>292</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>op</span>) = <span class='region red'>op</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L293' href='#L293'><pre>293</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                relation = Operator::from(op);</span></pre></td></tr><tr><td class='line-number'><a name='L294' href='#L294'><pre>294</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L295' href='#L295'><pre>295</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L296' href='#L296'><pre>296</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;property&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L297' href='#L297'><pre>297</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>property_list: Option&lt;Vec&lt;Property&gt;&gt; =</span></pre></td></tr><tr><td class='line-number'><a name='L298' href='#L298'><pre>298</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L299' href='#L299'><pre>299</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>property_list</span>) = <span class='region red'>property_list</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L300' href='#L300'><pre>300</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                property = property_list</span></pre></td></tr><tr><td class='line-number'><a name='L301' href='#L301'><pre>301</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                    .into_iter()</span></pre></td></tr><tr><td class='line-number'><a name='L302' href='#L302'><pre>302</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                    .filter(Property::is_valid)</span></pre></td></tr><tr><td class='line-number'><a name='L303' href='#L303'><pre>303</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                    .collect();</span></pre></td></tr><tr><td class='line-number'><a name='L304' href='#L304'><pre>304</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L305' href='#L305'><pre>305</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L306' href='#L306'><pre>306</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L307' href='#L307'><pre>307</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L308' href='#L308'><pre>308</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L309' href='#L309'><pre>309</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L310' href='#L310'><pre>310</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L311' href='#L311'><pre>311</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(ReportValue { relation, property })</span></pre></td></tr><tr><td class='line-number'><a name='L312' href='#L312'><pre>312</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L313' href='#L313'><pre>313</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L314' href='#L314'><pre>314</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L315' href='#L315'><pre>315</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(ReportValueVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L316' href='#L316'><pre>316</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L317' href='#L317'><pre>317</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L318' href='#L318'><pre>318</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L319' href='#L319'><pre>319</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize)]</pre></td></tr><tr><td class='line-number'><a name='L320' href='#L320'><pre>320</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(tag = &quot;dataType&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L321' href='#L321'><pre>321</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L322' href='#L322'><pre>322</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct Property {</pre></td></tr><tr><td class='line-number'><a name='L323' href='#L323'><pre>323</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub name: String,</pre></td></tr><tr><td class='line-number'><a name='L324' href='#L324'><pre>324</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub data_type: PropertyDataType,</pre></td></tr><tr><td class='line-number'><a name='L325' href='#L325'><pre>325</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub variants: Variants,</pre></td></tr><tr><td class='line-number'><a name='L326' href='#L326'><pre>326</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L327' href='#L327'><pre>327</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Property {</pre></td></tr><tr><td class='line-number'><a name='L328' href='#L328'><pre>328</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L329' href='#L329'><pre>329</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !self.name.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L330' href='#L330'><pre>330</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; <span class='region red'>!String::from(self.data_type).eq(&quot;none&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L331' href='#L331'><pre>331</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; <span class='region red'>self.variants.is_valid()</span></pre></td></tr><tr><td class='line-number'><a name='L332' href='#L332'><pre>332</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L333' href='#L333'><pre>333</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L334' href='#L334'><pre>334</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for Property {</pre></td></tr><tr><td class='line-number'><a name='L335' href='#L335'><pre>335</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L336' href='#L336'><pre>336</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L337' href='#L337'><pre>337</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L338' href='#L338'><pre>338</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L339' href='#L339'><pre>339</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct PropertyVisitor;</pre></td></tr><tr><td class='line-number'><a name='L340' href='#L340'><pre>340</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L341' href='#L341'><pre>341</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for PropertyVisitor {</pre></td></tr><tr><td class='line-number'><a name='L342' href='#L342'><pre>342</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = Property;</pre></td></tr><tr><td class='line-number'><a name='L343' href='#L343'><pre>343</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L344' href='#L344'><pre>344</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L345' href='#L345'><pre>345</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct EnumVariant&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L346' href='#L346'><pre>346</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L347' href='#L347'><pre>347</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L348' href='#L348'><pre>348</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L349' href='#L349'><pre>349</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L350' href='#L350'><pre>350</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L351' href='#L351'><pre>351</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L352' href='#L352'><pre>352</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut name = String::new();</span></pre></td></tr><tr><td class='line-number'><a name='L353' href='#L353'><pre>353</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut data_type = PropertyDataType::None;</span></pre></td></tr><tr><td class='line-number'><a name='L354' href='#L354'><pre>354</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut variants = Variants::None</span>;</pre></td></tr><tr><td class='line-number'><a name='L355' href='#L355'><pre>355</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L356' href='#L356'><pre>356</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L357' href='#L357'><pre>357</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;name&quot;</span> =&gt; <span class='region red'>name = map.next_value().unwrap_or_else(</span>|_| <span class='region red'>String::new()</span><span class='region red'>)</span>,</pre></td></tr><tr><td class='line-number'><a name='L358' href='#L358'><pre>358</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;dataType&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L359' href='#L359'><pre>359</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>op: Option&lt;String&gt; = map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L360' href='#L360'><pre>360</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>op</span>) = <span class='region red'>op</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L361' href='#L361'><pre>361</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                data_type = PropertyDataType::from(op);</span></pre></td></tr><tr><td class='line-number'><a name='L362' href='#L362'><pre>362</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L363' href='#L363'><pre>363</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L364' href='#L364'><pre>364</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;variants&quot;</span> =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L365' href='#L365'><pre>365</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            variants = map.next_value().unwrap_or(Variants::None);</span></pre></td></tr><tr><td class='line-number'><a name='L366' href='#L366'><pre>366</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        }</span></pre></td></tr><tr><td class='line-number'><a name='L367' href='#L367'><pre>367</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L368' href='#L368'><pre>368</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L369' href='#L369'><pre>369</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L370' href='#L370'><pre>370</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L371' href='#L371'><pre>371</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L372' href='#L372'><pre>372</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(Property {</span></pre></td></tr><tr><td class='line-number'><a name='L373' href='#L373'><pre>373</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    name,</span></pre></td></tr><tr><td class='line-number'><a name='L374' href='#L374'><pre>374</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    data_type,</span></pre></td></tr><tr><td class='line-number'><a name='L375' href='#L375'><pre>375</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    variants,</span></pre></td></tr><tr><td class='line-number'><a name='L376' href='#L376'><pre>376</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                })</span></pre></td></tr><tr><td class='line-number'><a name='L377' href='#L377'><pre>377</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L378' href='#L378'><pre>378</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L379' href='#L379'><pre>379</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L380' href='#L380'><pre>380</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(PropertyVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L381' href='#L381'><pre>381</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L382' href='#L382'><pre>382</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L383' href='#L383'><pre>383</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L384' href='#L384'><pre>384</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Default, Deserialize, Clone, Copy)]</pre></td></tr><tr><td class='line-number'><a name='L385' href='#L385'><pre>385</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;lowercase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L386' href='#L386'><pre>386</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum PropertyDataType {</pre></td></tr><tr><td class='line-number'><a name='L387' href='#L387'><pre>387</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[default]</pre></td></tr><tr><td class='line-number'><a name='L388' href='#L388'><pre>388</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    None,</pre></td></tr><tr><td class='line-number'><a name='L389' href='#L389'><pre>389</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Bool,</pre></td></tr><tr><td class='line-number'><a name='L390' href='#L390'><pre>390</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Int,</pre></td></tr><tr><td class='line-number'><a name='L391' href='#L391'><pre>391</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Double,</pre></td></tr><tr><td class='line-number'><a name='L392' href='#L392'><pre>392</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Enum,</pre></td></tr><tr><td class='line-number'><a name='L393' href='#L393'><pre>393</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L394' href='#L394'><pre>394</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl From&lt;PropertyDataType&gt; for String {</pre></td></tr><tr><td class='line-number'><a name='L395' href='#L395'><pre>395</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn from(value: PropertyDataType) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L396' href='#L396'><pre>396</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match value</span> {</pre></td></tr><tr><td class='line-number'><a name='L397' href='#L397'><pre>397</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            PropertyDataType::Bool =&gt; <span class='region red'>&quot;bool&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L398' href='#L398'><pre>398</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            PropertyDataType::Int =&gt; <span class='region red'>&quot;int&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L399' href='#L399'><pre>399</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            PropertyDataType::Double =&gt; <span class='region red'>&quot;double&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L400' href='#L400'><pre>400</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            PropertyDataType::Enum =&gt; <span class='region red'>&quot;enum&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L401' href='#L401'><pre>401</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            PropertyDataType::None =&gt; <span class='region red'>&quot;none&quot;.to_string()</span>,</pre></td></tr><tr><td class='line-number'><a name='L402' href='#L402'><pre>402</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L403' href='#L403'><pre>403</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L404' href='#L404'><pre>404</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L405' href='#L405'><pre>405</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl From&lt;String&gt; for PropertyDataType {</pre></td></tr><tr><td class='line-number'><a name='L406' href='#L406'><pre>406</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn from(value: String) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L407' href='#L407'><pre>407</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match value.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L408' href='#L408'><pre>408</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &quot;bool&quot;</span> =&gt; <span class='region red'>PropertyDataType::Bool</span>,</pre></td></tr><tr><td class='line-number'><a name='L409' href='#L409'><pre>409</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>&quot;int&quot;</span> =&gt; <span class='region red'>PropertyDataType::Int</span>,</pre></td></tr><tr><td class='line-number'><a name='L410' href='#L410'><pre>410</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>&quot;double&quot;</span> =&gt; <span class='region red'>PropertyDataType::Double</span>,</pre></td></tr><tr><td class='line-number'><a name='L411' href='#L411'><pre>411</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>&quot;enum&quot;</span> =&gt; <span class='region red'>PropertyDataType::Enum</span>,</pre></td></tr><tr><td class='line-number'><a name='L412' href='#L412'><pre>412</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            _ =&gt; <span class='region red'>PropertyDataType::None</span>,</pre></td></tr><tr><td class='line-number'><a name='L413' href='#L413'><pre>413</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L414' href='#L414'><pre>414</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L415' href='#L415'><pre>415</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L416' href='#L416'><pre>416</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L417' href='#L417'><pre>417</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Deserialize, Default)]</pre></td></tr><tr><td class='line-number'><a name='L418' href='#L418'><pre>418</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum Variants {</pre></td></tr><tr><td class='line-number'><a name='L419' href='#L419'><pre>419</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[default]</pre></td></tr><tr><td class='line-number'><a name='L420' href='#L420'><pre>420</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    None,</pre></td></tr><tr><td class='line-number'><a name='L421' href='#L421'><pre>421</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(rename = &quot;enumList&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L422' href='#L422'><pre>422</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Enum(Vec&lt;EnumVariant&gt;),</pre></td></tr><tr><td class='line-number'><a name='L423' href='#L423'><pre>423</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(rename = &quot;boolList&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L424' href='#L424'><pre>424</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Bool(Vec&lt;BoolVariant&gt;),</pre></td></tr><tr><td class='line-number'><a name='L425' href='#L425'><pre>425</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(rename = &quot;intStep&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L426' href='#L426'><pre>426</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    IntStep(IntStepVariant),</pre></td></tr><tr><td class='line-number'><a name='L427' href='#L427'><pre>427</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(rename = &quot;doubleStep&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L428' href='#L428'><pre>428</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    DoubleStep(DoubleStepVariant),</pre></td></tr><tr><td class='line-number'><a name='L429' href='#L429'><pre>429</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L430' href='#L430'><pre>430</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Variants {</pre></td></tr><tr><td class='line-number'><a name='L431' href='#L431'><pre>431</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L432' href='#L432'><pre>432</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match self</span> {</pre></td></tr><tr><td class='line-number'><a name='L433' href='#L433'><pre>433</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Variants::Enum(<span class='region red'>data_enum) =&gt; data_enum.iter().any(</span>|data| <span class='region red'>data.is_valid()</span><span class='region red'>)</span>,</pre></td></tr><tr><td class='line-number'><a name='L434' href='#L434'><pre>434</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Variants::Bool(<span class='region red'>data_bool) =&gt; !data_bool.is_empty()</span>,</pre></td></tr><tr><td class='line-number'><a name='L435' href='#L435'><pre>435</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Variants::IntStep(<span class='region red'>data_int_step) =&gt; data_int_step.is_valid()</span>,</pre></td></tr><tr><td class='line-number'><a name='L436' href='#L436'><pre>436</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Variants::DoubleStep(<span class='region red'>data_double_step) =&gt; data_double_step.is_valid()</span>,</pre></td></tr><tr><td class='line-number'><a name='L437' href='#L437'><pre>437</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            _ =&gt; <span class='region red'>false</span>,</pre></td></tr><tr><td class='line-number'><a name='L438' href='#L438'><pre>438</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L439' href='#L439'><pre>439</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L440' href='#L440'><pre>440</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L441' href='#L441'><pre>441</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L442' href='#L442'><pre>442</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>#[derive(Debug, Serialize, <span class='region red'>Deserialize</span>)]</pre></td></tr><tr><td class='line-number'><a name='L443' href='#L443'><pre>443</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L444' href='#L444'><pre>444</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct IntStepVariant {</pre></td></tr><tr><td class='line-number'><a name='L445' href='#L445'><pre>445</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_or_int&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L446' href='#L446'><pre>446</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub min_value: i32,</pre></td></tr><tr><td class='line-number'><a name='L447' href='#L447'><pre>447</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_or_int&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L448' href='#L448'><pre>448</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub max_value: i32,</pre></td></tr><tr><td class='line-number'><a name='L449' href='#L449'><pre>449</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_or_int&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L450' href='#L450'><pre>450</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub step: i32,</pre></td></tr><tr><td class='line-number'><a name='L451' href='#L451'><pre>451</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub unit: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L452' href='#L452'><pre>452</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L453' href='#L453'><pre>453</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for IntStepVariant {</pre></td></tr><tr><td class='line-number'><a name='L454' href='#L454'><pre>454</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L455' href='#L455'><pre>455</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.min_value != 9999999</span> &amp;&amp; <span class='region red'>self.max_value != 9999999</span> &amp;&amp; <span class='region red'>self.step != 9999999</span></pre></td></tr><tr><td class='line-number'><a name='L456' href='#L456'><pre>456</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L457' href='#L457'><pre>457</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L458' href='#L458'><pre>458</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L459' href='#L459'><pre>459</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>#[derive(Debug, Serialize, <span class='region red'>Deserialize</span>)]</pre></td></tr><tr><td class='line-number'><a name='L460' href='#L460'><pre>460</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L461' href='#L461'><pre>461</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct DoubleStepVariant {</pre></td></tr><tr><td class='line-number'><a name='L462' href='#L462'><pre>462</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_int_or_float&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L463' href='#L463'><pre>463</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub min_value: f32,</pre></td></tr><tr><td class='line-number'><a name='L464' href='#L464'><pre>464</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_int_or_float&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L465' href='#L465'><pre>465</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub max_value: f32,</pre></td></tr><tr><td class='line-number'><a name='L466' href='#L466'><pre>466</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_int_or_float&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L467' href='#L467'><pre>467</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub step: f32,</pre></td></tr><tr><td class='line-number'><a name='L468' href='#L468'><pre>468</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub unit: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L469' href='#L469'><pre>469</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L470' href='#L470'><pre>470</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for DoubleStepVariant {</pre></td></tr><tr><td class='line-number'><a name='L471' href='#L471'><pre>471</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L472' href='#L472'><pre>472</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.min_value != 9999999.0</span> &amp;&amp; <span class='region red'>self.max_value != 9999999.0</span> &amp;&amp; <span class='region red'>self.step != 9999999.0</span></pre></td></tr><tr><td class='line-number'><a name='L473' href='#L473'><pre>473</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L474' href='#L474'><pre>474</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L475' href='#L475'><pre>475</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L476' href='#L476'><pre>476</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize)]</pre></td></tr><tr><td class='line-number'><a name='L477' href='#L477'><pre>477</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L478' href='#L478'><pre>478</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct EnumVariant {</pre></td></tr><tr><td class='line-number'><a name='L479' href='#L479'><pre>479</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub std_value: String,</pre></td></tr><tr><td class='line-number'><a name='L480' href='#L480'><pre>480</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub description: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L481' href='#L481'><pre>481</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L482' href='#L482'><pre>482</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for EnumVariant {</pre></td></tr><tr><td class='line-number'><a name='L483' href='#L483'><pre>483</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L484' href='#L484'><pre>484</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !self.std_value.eq(&quot;std_value_null&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L485' href='#L485'><pre>485</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L486' href='#L486'><pre>486</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L487' href='#L487'><pre>487</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L488' href='#L488'><pre>488</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for EnumVariant {</pre></td></tr><tr><td class='line-number'><a name='L489' href='#L489'><pre>489</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L490' href='#L490'><pre>490</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L491' href='#L491'><pre>491</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L492' href='#L492'><pre>492</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    </span>{</pre></td></tr><tr><td class='line-number'><a name='L493' href='#L493'><pre>493</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        struct EnumVariantVisitor;</pre></td></tr><tr><td class='line-number'><a name='L494' href='#L494'><pre>494</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L495' href='#L495'><pre>495</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for EnumVariantVisitor {</pre></td></tr><tr><td class='line-number'><a name='L496' href='#L496'><pre>496</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            type Value = EnumVariant;</pre></td></tr><tr><td class='line-number'><a name='L497' href='#L497'><pre>497</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L498' href='#L498'><pre>498</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L499' href='#L499'><pre>499</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                formatter.write_str(&quot;struct EnumVariant&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L500' href='#L500'><pre>500</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span></pre></td></tr><tr><td class='line-number'><a name='L501' href='#L501'><pre>501</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L502' href='#L502'><pre>502</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L503' href='#L503'><pre>503</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            where</span></pre></td></tr><tr><td class='line-number'><a name='L504' href='#L504'><pre>504</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                A: serde::de::MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L505' href='#L505'><pre>505</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            {</span></pre></td></tr><tr><td class='line-number'><a name='L506' href='#L506'><pre>506</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut std_value = &quot;std_value_null&quot;.to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L507' href='#L507'><pre>507</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut description = None</span>;</pre></td></tr><tr><td class='line-number'><a name='L508' href='#L508'><pre>508</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L509' href='#L509'><pre>509</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L510' href='#L510'><pre>510</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        &quot;stdValue&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L511' href='#L511'><pre>511</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let <span class='region red'>op: Option&lt;String&gt; = map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L512' href='#L512'><pre>512</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            if let Some(<span class='region red'>op</span>) = <span class='region red'>op</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L513' href='#L513'><pre>513</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                                std_value = op;</span></pre></td></tr><tr><td class='line-number'><a name='L514' href='#L514'><pre>514</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L515' href='#L515'><pre>515</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L516' href='#L516'><pre>516</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>&quot;description&quot;</span> =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L517' href='#L517'><pre>517</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            description = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L518' href='#L518'><pre>518</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        }</span></pre></td></tr><tr><td class='line-number'><a name='L519' href='#L519'><pre>519</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L520' href='#L520'><pre>520</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            let _: <span class='region red'>serde::de::IgnoredAny</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L521' href='#L521'><pre>521</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L522' href='#L522'><pre>522</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L523' href='#L523'><pre>523</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L524' href='#L524'><pre>524</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(EnumVariant {</span></pre></td></tr><tr><td class='line-number'><a name='L525' href='#L525'><pre>525</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    std_value,</span></pre></td></tr><tr><td class='line-number'><a name='L526' href='#L526'><pre>526</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    description,</span></pre></td></tr><tr><td class='line-number'><a name='L527' href='#L527'><pre>527</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                })</span></pre></td></tr><tr><td class='line-number'><a name='L528' href='#L528'><pre>528</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L529' href='#L529'><pre>529</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L530' href='#L530'><pre>530</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L531' href='#L531'><pre>531</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>deserializer.deserialize_map(EnumVariantVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L532' href='#L532'><pre>532</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L533' href='#L533'><pre>533</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L534' href='#L534'><pre>534</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L535' href='#L535'><pre>535</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>#[derive(Debug, Serialize, <span class='region red'>Deserialize</span>)]</pre></td></tr><tr><td class='line-number'><a name='L536' href='#L536'><pre>536</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L537' href='#L537'><pre>537</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct BoolVariant {</pre></td></tr><tr><td class='line-number'><a name='L538' href='#L538'><pre>538</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[serde(deserialize_with = &quot;deserialize_string_to_bool&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L539' href='#L539'><pre>539</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub std_value: bool,</pre></td></tr><tr><td class='line-number'><a name='L540' href='#L540'><pre>540</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub description: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L541' href='#L541'><pre>541</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr></table></div></body></html>