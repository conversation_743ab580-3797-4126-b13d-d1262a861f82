<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../style.css'><script src='../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/resource_rust/rust_resource/src/api/resource.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use super::resource_callback::ResourceListCallback;</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use super::{error::ResourceError, resource_callback::ResourceCallback};</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::api::resource_manager::ResourceManager;</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::installers::installer::Installer;</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::installers::preset_installer::PresetInstaller;</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::installers::uninstaller::Uninstaller;</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::models::condition::from_func::FROM_FUNC_COMMON_RESOURCE;</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::models::condition::{DeviceResourceCondition, NormalResourceCondition};</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::models::{</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    condition::DeviceCondition,</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    resource_info::{ResourceFilter, ResourceInfo, ResourceType},</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>};</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use log::debug;</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::thread;</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use tokio::runtime::Runtime;</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum ResourcePlatform {</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    ResourceAppPlatformChina = 0,         //智家</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    ResourceAppPlatformSoutheastAsia = 1, //海外</pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Unknown = 2,</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum ResourceRequestEnvironment {</pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    RequestEnvProduction = 0,</pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    RequestEnvAcceptance = 1,</pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Unknown = 2,</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceSetting {</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    app_version: String,</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    platform: ResourcePlatform,</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    request_environment: ResourceRequestEnvironment,</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    is_gray_mode: bool,</pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl ResourceSetting {</pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn new(</span></pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        app_version: String,</span></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        platform: ResourcePlatform,</span></pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        request_environment: ResourceRequestEnvironment,</span></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        is_gray_mode: bool,</span></pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceSetting {</span></pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            app_version,</span></pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            platform,</span></pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            request_environment,</span></pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            is_gray_mode,</span></pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_app_version(&amp;self) -&gt; &amp;str {</span></pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self.app_version</span></pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_platform(&amp;self) -&gt; &amp;ResourcePlatform {</span></pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self.platform</span></pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_request_environment(&amp;self) -&gt; &amp;ResourceRequestEnvironment {</span></pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self.request_environment</span></pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_gray_mode(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.is_gray_mode</span></pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug)]</pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct UPResource;</pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Default for UPResource {</pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Self::new()</span></pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl UPResource {</pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn new() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        UPResource {}</span></pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 预置资源列表</pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param preset_bundle_dir 预置资源包所在的bundle目录(zip文件的父文件夹路径)</pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 预置结果列表，错误信息</pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn preset_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        preset_bundle_dir: String,</span></pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        callback: Option&lt;Box&lt;dyn ResourceListCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;(), ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            if let Some(<span class='region red'>cb</span>) = <span class='region red'>callback</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                cb.on_list_result(</span></pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;vec![],</span></pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    Some(&quot;Cache is being cleaned, operation cannot be performed&quot;.to_string()),</span></pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                );</span></pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        PresetInstaller::install_resources(preset_bundle_dir, callback)</span></pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /***</pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 预置资源,卡线程，会阻塞后续代码执行</pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param preset_bundle_dir 预置资源包所在的bundle目录(zip文件的父文件夹路径)</pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 预置结果，错误信息</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn preset_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        preset_bundle_dir: String,</span></pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Option&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if name.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::IllegalParameters(</span></pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;name is empty&quot;.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ))</span>;</pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        PresetInstaller::install_resource_sync(preset_bundle_dir, name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /***</pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 查询普通资源列表</pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 查询结果列表，错误信息 */</pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn search_normal_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Vec&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if name.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::IllegalParameters(</span></pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;name is empty&quot;.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ))</span>;</pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        if <span class='region red'>matches!</span>(<span class='region red'>res_type</span>, ResourceType::DeviceConfig) {</pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::IllegalParameters(</span></pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;resType is DeviceConfig&quot;.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ))</span>;</pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .search_normal_resource_list(name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /***</pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 查询设备资源列表</pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param deviceCondition 设备条件</pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 查询结果列表，错误信息 */</pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn search_device_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        device_condition: DeviceCondition,</span></pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Vec&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .search_device_resource_list(device_condition)</span></pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 请求普通资源列表</pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 请求结果列表，错误信息</pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub async fn request_normal_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L179' href='#L179'><pre>179</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L180' href='#L180'><pre>180</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Vec&lt;ResourceInfo&gt;, ResourceError&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L181' href='#L181'><pre>181</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L182' href='#L182'><pre>182</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L183' href='#L183'><pre>183</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L184' href='#L184'><pre>184</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let condition: NormalResourceCondition = NormalResourceCondition::new(</span></pre></td></tr><tr><td class='line-number'><a name='L185' href='#L185'><pre>185</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            FROM_FUNC_COMMON_RESOURCE.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L186' href='#L186'><pre>186</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            res_type,</span></pre></td></tr><tr><td class='line-number'><a name='L187' href='#L187'><pre>187</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            name,</span></pre></td></tr><tr><td class='line-number'><a name='L188' href='#L188'><pre>188</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L189' href='#L189'><pre>189</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .get_resource_setting()</span></pre></td></tr><tr><td class='line-number'><a name='L190' href='#L190'><pre>190</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .get_app_version()</span></pre></td></tr><tr><td class='line-number'><a name='L191' href='#L191'><pre>191</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L192' href='#L192'><pre>192</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        );</span></pre></td></tr><tr><td class='line-number'><a name='L193' href='#L193'><pre>193</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L194' href='#L194'><pre>194</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_request_handler()</span></pre></td></tr><tr><td class='line-number'><a name='L195' href='#L195'><pre>195</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .request_normal_resource_list(condition)</span></pre></td></tr><tr><td class='line-number'><a name='L196' href='#L196'><pre>196</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            .<span class='region red'>await</span></pre></td></tr><tr><td class='line-number'><a name='L197' href='#L197'><pre>197</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L198' href='#L198'><pre>198</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L199' href='#L199'><pre>199</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 请求设备资源列表</pre></td></tr><tr><td class='line-number'><a name='L200' href='#L200'><pre>200</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param deviceCondition 设备条件</pre></td></tr><tr><td class='line-number'><a name='L201' href='#L201'><pre>201</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 请求结果列表，错误信息</pre></td></tr><tr><td class='line-number'><a name='L202' href='#L202'><pre>202</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L203' href='#L203'><pre>203</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub async fn request_device_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L204' href='#L204'><pre>204</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L205' href='#L205'><pre>205</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        device_condition: DeviceCondition,</span></pre></td></tr><tr><td class='line-number'><a name='L206' href='#L206'><pre>206</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Vec&lt;ResourceInfo&gt;, ResourceError&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L207' href='#L207'><pre>207</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        debug!</span>(<span class='region red'>&quot;rust_resource resource request_device_resource_list start&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L208' href='#L208'><pre>208</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        if <span class='region red'>self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L209' href='#L209'><pre>209</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L210' href='#L210'><pre>210</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L211' href='#L211'><pre>211</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let version = ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L212' href='#L212'><pre>212</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource_setting()</span></pre></td></tr><tr><td class='line-number'><a name='L213' href='#L213'><pre>213</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_app_version()</span></pre></td></tr><tr><td class='line-number'><a name='L214' href='#L214'><pre>214</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L215' href='#L215'><pre>215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let resource_condition = DeviceResourceCondition::new(version, device_condition)</span>;</pre></td></tr><tr><td class='line-number'><a name='L216' href='#L216'><pre>216</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        let <span class='region red'>result</span> = <span class='region red'>ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L217' href='#L217'><pre>217</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_request_handler()</span></pre></td></tr><tr><td class='line-number'><a name='L218' href='#L218'><pre>218</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .request_device_resource_list(resource_condition)</span></pre></td></tr><tr><td class='line-number'><a name='L219' href='#L219'><pre>219</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            .<span class='region red'>await</span>;</pre></td></tr><tr><td class='line-number'><a name='L220' href='#L220'><pre>220</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>debug!</span>(<span class='region red'>&quot;rust_resource resource request_device_resource_list end&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L221' href='#L221'><pre>221</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>result</span></pre></td></tr><tr><td class='line-number'><a name='L222' href='#L222'><pre>222</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L223' href='#L223'><pre>223</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L224' href='#L224'><pre>224</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 安装资源包</pre></td></tr><tr><td class='line-number'><a name='L225' href='#L225'><pre>225</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 配合eventbus使用，监听安装进度和安装结果</pre></td></tr><tr><td class='line-number'><a name='L226' href='#L226'><pre>226</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     *  @param resourceInfo 资源信息</pre></td></tr><tr><td class='line-number'><a name='L227' href='#L227'><pre>227</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resource_callback 下载进度回调</pre></td></tr><tr><td class='line-number'><a name='L228' href='#L228'><pre>228</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 任务id，错误信息</pre></td></tr><tr><td class='line-number'><a name='L229' href='#L229'><pre>229</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L230' href='#L230'><pre>230</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn install(</span></pre></td></tr><tr><td class='line-number'><a name='L231' href='#L231'><pre>231</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L232' href='#L232'><pre>232</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_info: ResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L233' href='#L233'><pre>233</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_callback: Option&lt;Box&lt;dyn ResourceCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L234' href='#L234'><pre>234</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;String, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L235' href='#L235'><pre>235</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        debug!</span>(<span class='region red'>&quot;rust_resource resource install start&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L236' href='#L236'><pre>236</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        if <span class='region red'>self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L237' href='#L237'><pre>237</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            if let Some(<span class='region red'>cb</span>) = <span class='region red'>resource_callback</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L238' href='#L238'><pre>238</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                cb.on_result(</span></pre></td></tr><tr><td class='line-number'><a name='L239' href='#L239'><pre>239</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;resource_info,</span></pre></td></tr><tr><td class='line-number'><a name='L240' href='#L240'><pre>240</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    Some(&quot;Cache is being cleaned, operation cannot be performed&quot;.to_string()),</span></pre></td></tr><tr><td class='line-number'><a name='L241' href='#L241'><pre>241</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                );</span></pre></td></tr><tr><td class='line-number'><a name='L242' href='#L242'><pre>242</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L243' href='#L243'><pre>243</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L244' href='#L244'><pre>244</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L245' href='#L245'><pre>245</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let result = Installer::install(resource_info, resource_callback);</span></pre></td></tr><tr><td class='line-number'><a name='L246' href='#L246'><pre>246</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        debug!</span>(<span class='region red'>&quot;rust_resource resource install end&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L247' href='#L247'><pre>247</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>result</span></pre></td></tr><tr><td class='line-number'><a name='L248' href='#L248'><pre>248</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L249' href='#L249'><pre>249</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L250' href='#L250'><pre>250</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 取消资源安装、或者卸载</pre></td></tr><tr><td class='line-number'><a name='L251' href='#L251'><pre>251</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param taskId 任务id</pre></td></tr><tr><td class='line-number'><a name='L252' href='#L252'><pre>252</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 错误信息</pre></td></tr><tr><td class='line-number'><a name='L253' href='#L253'><pre>253</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L254' href='#L254'><pre>254</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn cancel(&amp;self, task_id: &amp;str) -&gt; Result&lt;(), ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L255' href='#L255'><pre>255</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Installer::cancel(task_id)</span></pre></td></tr><tr><td class='line-number'><a name='L256' href='#L256'><pre>256</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L257' href='#L257'><pre>257</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L258' href='#L258'><pre>258</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 卸载资源包</pre></td></tr><tr><td class='line-number'><a name='L259' href='#L259'><pre>259</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 配合eventbus使用，监听卸载结果</pre></td></tr><tr><td class='line-number'><a name='L260' href='#L260'><pre>260</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * eventbus 资源包本身比较小，因此卸载没有进度，只有安装结果使用&quot;resourceId_uninstall_result&quot;</pre></td></tr><tr><td class='line-number'><a name='L261' href='#L261'><pre>261</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resourceInfo 资源信息</pre></td></tr><tr><td class='line-number'><a name='L262' href='#L262'><pre>262</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 任务id，错误信息</pre></td></tr><tr><td class='line-number'><a name='L263' href='#L263'><pre>263</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L264' href='#L264'><pre>264</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn uninstall(</span></pre></td></tr><tr><td class='line-number'><a name='L265' href='#L265'><pre>265</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L266' href='#L266'><pre>266</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_info: ResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L267' href='#L267'><pre>267</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        callback: Option&lt;Box&lt;dyn ResourceCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L268' href='#L268'><pre>268</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;String, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L269' href='#L269'><pre>269</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L270' href='#L270'><pre>270</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            if let Some(<span class='region red'>callback</span>) = <span class='region red'>callback</span> {</pre></td></tr><tr><td class='line-number'><a name='L271' href='#L271'><pre>271</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>callback.on_result(</span></pre></td></tr><tr><td class='line-number'><a name='L272' href='#L272'><pre>272</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;resource_info,</span></pre></td></tr><tr><td class='line-number'><a name='L273' href='#L273'><pre>273</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    Some(&quot;Cache is being cleaned, operation cannot be performed&quot;.to_string()),</span></pre></td></tr><tr><td class='line-number'><a name='L274' href='#L274'><pre>274</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                )</span></pre></td></tr><tr><td class='line-number'><a name='L275' href='#L275'><pre>275</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L276' href='#L276'><pre>276</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L277' href='#L277'><pre>277</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L278' href='#L278'><pre>278</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Uninstaller::uninstall(resource_info, callback)</span></pre></td></tr><tr><td class='line-number'><a name='L279' href='#L279'><pre>279</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L280' href='#L280'><pre>280</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L281' href='#L281'><pre>281</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 查询普通资源并安装，此操作会先查询资源，然后安装，安装过程不可取消</pre></td></tr><tr><td class='line-number'><a name='L282' href='#L282'><pre>282</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 配合eventbus使用，监听安装进度和安装结果</pre></td></tr><tr><td class='line-number'><a name='L283' href='#L283'><pre>283</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L284' href='#L284'><pre>284</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L285' href='#L285'><pre>285</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resource_callback 下载进度回调</pre></td></tr><tr><td class='line-number'><a name='L286' href='#L286'><pre>286</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 安装结果，错误信息</pre></td></tr><tr><td class='line-number'><a name='L287' href='#L287'><pre>287</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L288' href='#L288'><pre>288</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn query_and_install_normal_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L289' href='#L289'><pre>289</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L290' href='#L290'><pre>290</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L291' href='#L291'><pre>291</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L292' href='#L292'><pre>292</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_callback: Option&lt;Box&lt;dyn ResourceCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L293' href='#L293'><pre>293</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) {</span></pre></td></tr><tr><td class='line-number'><a name='L294' href='#L294'><pre>294</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        thread::spawn(</span>move || <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L295' href='#L295'><pre>295</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let rt = Runtime::new().expect(&quot;Failed to create Tokio runtime&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L296' href='#L296'><pre>296</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            rt.block_on(</span>async <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L297' href='#L297'><pre>297</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let resource = ResourceManager::get_instance().get_resource()</span>;</pre></td></tr><tr><td class='line-number'><a name='L298' href='#L298'><pre>298</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                let <span class='region red'>result</span> = <span class='region red'>resource.request_normal_resource_list(name, res_type)</span>.<span class='region red'>await</span>;</pre></td></tr><tr><td class='line-number'><a name='L299' href='#L299'><pre>299</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                match <span class='region red'>result</span> {</pre></td></tr><tr><td class='line-number'><a name='L300' href='#L300'><pre>300</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    Ok(<span class='region red'>resource_list) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L301' href='#L301'><pre>301</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        resource.query_resource_info_and_install(resource_callback, resource_list)</span></pre></td></tr><tr><td class='line-number'><a name='L302' href='#L302'><pre>302</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L303' href='#L303'><pre>303</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    Err(<span class='region red'>e</span>) =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L304' href='#L304'><pre>304</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        if let Some(<span class='region red'>callback</span>) = <span class='region red'>resource_callback</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L305' href='#L305'><pre>305</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            callback.on_result(&amp;ResourceInfo::default(), Some(e.to_string()));</span></pre></td></tr><tr><td class='line-number'><a name='L306' href='#L306'><pre>306</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L307' href='#L307'><pre>307</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        <span class='region red'>Err(e)</span></pre></td></tr><tr><td class='line-number'><a name='L308' href='#L308'><pre>308</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L309' href='#L309'><pre>309</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L310' href='#L310'><pre>310</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L311' href='#L311'><pre>311</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span><span class='region red'>);</span></pre></td></tr><tr><td class='line-number'><a name='L312' href='#L312'><pre>312</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L313' href='#L313'><pre>313</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L314' href='#L314'><pre>314</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 查询设备资源并安装，此操作会先查询资源，然后安装，安装过程不可取消</pre></td></tr><tr><td class='line-number'><a name='L315' href='#L315'><pre>315</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 配合eventbus使用，监听安装进度和安装结果</pre></td></tr><tr><td class='line-number'><a name='L316' href='#L316'><pre>316</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param deviceCondition 设备条件</pre></td></tr><tr><td class='line-number'><a name='L317' href='#L317'><pre>317</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resource_callback 下载进度回调</pre></td></tr><tr><td class='line-number'><a name='L318' href='#L318'><pre>318</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 安装结果，错误信息</pre></td></tr><tr><td class='line-number'><a name='L319' href='#L319'><pre>319</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L320' href='#L320'><pre>320</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn query_and_install_device_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L321' href='#L321'><pre>321</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L322' href='#L322'><pre>322</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        device_condition: DeviceCondition,</span></pre></td></tr><tr><td class='line-number'><a name='L323' href='#L323'><pre>323</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_callback: Option&lt;Box&lt;dyn ResourceCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L324' href='#L324'><pre>324</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) {</span></pre></td></tr><tr><td class='line-number'><a name='L325' href='#L325'><pre>325</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        thread::spawn(</span>move || <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L326' href='#L326'><pre>326</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let rt = Runtime::new().expect(&quot;Failed to create Tokio runtime&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L327' href='#L327'><pre>327</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            rt.block_on(</span>async <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L328' href='#L328'><pre>328</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                debug!</span>(<span class='region red'>&quot;rust_resource resource query_and_install_device_resource start&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L329' href='#L329'><pre>329</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                let <span class='region red'>resource = ResourceManager::get_instance().get_resource()</span>;</pre></td></tr><tr><td class='line-number'><a name='L330' href='#L330'><pre>330</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                let <span class='region red'>result</span> = <span class='region red'>resource</span></pre></td></tr><tr><td class='line-number'><a name='L331' href='#L331'><pre>331</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .request_device_resource_list(device_condition)</span></pre></td></tr><tr><td class='line-number'><a name='L332' href='#L332'><pre>332</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    .<span class='region red'>await</span>;</pre></td></tr><tr><td class='line-number'><a name='L333' href='#L333'><pre>333</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>debug!</span>(</pre></td></tr><tr><td class='line-number'><a name='L334' href='#L334'><pre>334</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>&quot;rust_resource resource request_device_resource_list result = {:?}&quot;,</span></pre></td></tr><tr><td class='line-number'><a name='L335' href='#L335'><pre>335</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;result</span></pre></td></tr><tr><td class='line-number'><a name='L336' href='#L336'><pre>336</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                );</pre></td></tr><tr><td class='line-number'><a name='L337' href='#L337'><pre>337</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                match <span class='region red'>result</span> {</pre></td></tr><tr><td class='line-number'><a name='L338' href='#L338'><pre>338</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    Ok(<span class='region red'>resource_list) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L339' href='#L339'><pre>339</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        debug!</span>(<span class='region red'>&quot;rust_resource resource request_device_resource_list Ok&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L340' href='#L340'><pre>340</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        let <span class='region red'>res_str = resource</span></pre></td></tr><tr><td class='line-number'><a name='L341' href='#L341'><pre>341</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            .query_resource_info_and_install(resource_callback, resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L342' href='#L342'><pre>342</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        debug!</span>(</pre></td></tr><tr><td class='line-number'><a name='L343' href='#L343'><pre>343</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            <span class='region red'>&quot;rust_resource resource request_device_resource_list Ok result : {:?}&quot;,</span></pre></td></tr><tr><td class='line-number'><a name='L344' href='#L344'><pre>344</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            &amp;res_str</span></pre></td></tr><tr><td class='line-number'><a name='L345' href='#L345'><pre>345</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        );</pre></td></tr><tr><td class='line-number'><a name='L346' href='#L346'><pre>346</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L347' href='#L347'><pre>347</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    Err(<span class='region red'>e</span>) =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L348' href='#L348'><pre>348</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        if let Some(<span class='region red'>callback</span>) = <span class='region red'>resource_callback</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L349' href='#L349'><pre>349</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            callback.on_result(&amp;ResourceInfo::default(), Some(e.to_string()));</span></pre></td></tr><tr><td class='line-number'><a name='L350' href='#L350'><pre>350</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L351' href='#L351'><pre>351</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L352' href='#L352'><pre>352</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L353' href='#L353'><pre>353</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>}</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L354' href='#L354'><pre>354</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span><span class='region red'>);</span></pre></td></tr><tr><td class='line-number'><a name='L355' href='#L355'><pre>355</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L356' href='#L356'><pre>356</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L357' href='#L357'><pre>357</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn query_resource_info_and_install(</span></pre></td></tr><tr><td class='line-number'><a name='L358' href='#L358'><pre>358</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L359' href='#L359'><pre>359</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_callback: Option&lt;Box&lt;dyn ResourceCallback&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L360' href='#L360'><pre>360</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_list: Vec&lt;ResourceInfo&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L361' href='#L361'><pre>361</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;String, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L362' href='#L362'><pre>362</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        debug!</span>(<span class='region red'>&quot;rust_resource resource query_resource_info_and_install start&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L363' href='#L363'><pre>363</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        if <span class='region red'>resource_list.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L364' href='#L364'><pre>364</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>debug!</span>(<span class='region red'>&quot;rust_resource resource query_resource_info_and_install resource_list.is_empty&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L365' href='#L365'><pre>365</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            if let Some(<span class='region red'>callback</span>) = <span class='region red'>resource_callback</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L366' href='#L366'><pre>366</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                callback.on_result(</span></pre></td></tr><tr><td class='line-number'><a name='L367' href='#L367'><pre>367</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;ResourceInfo::default(),</span></pre></td></tr><tr><td class='line-number'><a name='L368' href='#L368'><pre>368</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    Some(&quot;Resource not find&quot;.to_string()),</span></pre></td></tr><tr><td class='line-number'><a name='L369' href='#L369'><pre>369</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                );</span></pre></td></tr><tr><td class='line-number'><a name='L370' href='#L370'><pre>370</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L371' href='#L371'><pre>371</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>Err(ResourceError::EmptyResultError(</span></pre></td></tr><tr><td class='line-number'><a name='L372' href='#L372'><pre>372</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;Resource not find&quot;.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L373' href='#L373'><pre>373</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ))</span></pre></td></tr><tr><td class='line-number'><a name='L374' href='#L374'><pre>374</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        } else {</pre></td></tr><tr><td class='line-number'><a name='L375' href='#L375'><pre>375</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>debug!</span>(<span class='region red'>&quot;rust_resource resource query_resource_info_and_install install&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L376' href='#L376'><pre>376</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            let <span class='region red'>resource_info</span> = <span class='region red'>resource_list</span></pre></td></tr><tr><td class='line-number'><a name='L377' href='#L377'><pre>377</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .first()</span></pre></td></tr><tr><td class='line-number'><a name='L378' href='#L378'><pre>378</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .ok_or(ResourceError::EmptyResultError(</span></pre></td></tr><tr><td class='line-number'><a name='L379' href='#L379'><pre>379</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &quot;Resource not find&quot;.to_string(),</span></pre></td></tr><tr><td class='line-number'><a name='L380' href='#L380'><pre>380</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ))</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L381' href='#L381'><pre>381</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            let <span class='region red'>task_id</span> = <span class='region red'>self.install(resource_info.clone(), resource_callback)</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L382' href='#L382'><pre>382</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>debug!</span>(<span class='region red'>&quot;rust_resource resource query_resource_info_and_install install finish task_id = {}&quot;</span>, task_id);</pre></td></tr><tr><td class='line-number'><a name='L383' href='#L383'><pre>383</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>Ok(task_id)</span></pre></td></tr><tr><td class='line-number'><a name='L384' href='#L384'><pre>384</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L385' href='#L385'><pre>385</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L386' href='#L386'><pre>386</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L387' href='#L387'><pre>387</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 获取全部资源列表</pre></td></tr><tr><td class='line-number'><a name='L388' href='#L388'><pre>388</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param filter 资源过滤器，可以通过资源的任务状态、信息进行过滤</pre></td></tr><tr><td class='line-number'><a name='L389' href='#L389'><pre>389</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 全部资源列表，错误信息</pre></td></tr><tr><td class='line-number'><a name='L390' href='#L390'><pre>390</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L391' href='#L391'><pre>391</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_entire_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L392' href='#L392'><pre>392</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L393' href='#L393'><pre>393</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        filter: Option&lt;Box&lt;dyn ResourceFilter&gt;&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L394' href='#L394'><pre>394</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Vec&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L395' href='#L395'><pre>395</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L396' href='#L396'><pre>396</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L397' href='#L397'><pre>397</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L398' href='#L398'><pre>398</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L399' href='#L399'><pre>399</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L400' href='#L400'><pre>400</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_entire_resource_list(filter)</span></pre></td></tr><tr><td class='line-number'><a name='L401' href='#L401'><pre>401</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L402' href='#L402'><pre>402</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L403' href='#L403'><pre>403</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 获取最新资源，获取出来的资源，可能安装，也可能未安装</pre></td></tr><tr><td class='line-number'><a name='L404' href='#L404'><pre>404</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L405' href='#L405'><pre>405</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L406' href='#L406'><pre>406</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 最新资源，错误信息</pre></td></tr><tr><td class='line-number'><a name='L407' href='#L407'><pre>407</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L408' href='#L408'><pre>408</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_latest_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L409' href='#L409'><pre>409</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L410' href='#L410'><pre>410</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L411' href='#L411'><pre>411</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L412' href='#L412'><pre>412</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Option&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L413' href='#L413'><pre>413</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L414' href='#L414'><pre>414</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L415' href='#L415'><pre>415</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L416' href='#L416'><pre>416</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L417' href='#L417'><pre>417</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L418' href='#L418'><pre>418</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_latest_resource(name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L419' href='#L419'><pre>419</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L420' href='#L420'><pre>420</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /***</pre></td></tr><tr><td class='line-number'><a name='L421' href='#L421'><pre>421</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 获取最新安装的资源</pre></td></tr><tr><td class='line-number'><a name='L422' href='#L422'><pre>422</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L423' href='#L423'><pre>423</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L424' href='#L424'><pre>424</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 最新安装的资源，错误信息</pre></td></tr><tr><td class='line-number'><a name='L425' href='#L425'><pre>425</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L426' href='#L426'><pre>426</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_latest_installed_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L427' href='#L427'><pre>427</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L428' href='#L428'><pre>428</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: String,</span></pre></td></tr><tr><td class='line-number'><a name='L429' href='#L429'><pre>429</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L430' href='#L430'><pre>430</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Option&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L431' href='#L431'><pre>431</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L432' href='#L432'><pre>432</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L433' href='#L433'><pre>433</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L434' href='#L434'><pre>434</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L435' href='#L435'><pre>435</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L436' href='#L436'><pre>436</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_latest_installed_resource(name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L437' href='#L437'><pre>437</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L438' href='#L438'><pre>438</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L439' href='#L439'><pre>439</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 获取指定资源</pre></td></tr><tr><td class='line-number'><a name='L440' href='#L440'><pre>440</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param name 资源名称</pre></td></tr><tr><td class='line-number'><a name='L441' href='#L441'><pre>441</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L442' href='#L442'><pre>442</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param version 资源版本</pre></td></tr><tr><td class='line-number'><a name='L443' href='#L443'><pre>443</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 指定资源，错误信息</pre></td></tr><tr><td class='line-number'><a name='L444' href='#L444'><pre>444</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L445' href='#L445'><pre>445</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L446' href='#L446'><pre>446</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L447' href='#L447'><pre>447</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: &amp;str,</span></pre></td></tr><tr><td class='line-number'><a name='L448' href='#L448'><pre>448</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: &amp;ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L449' href='#L449'><pre>449</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        version: &amp;str,</span></pre></td></tr><tr><td class='line-number'><a name='L450' href='#L450'><pre>450</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Option&lt;ResourceInfo&gt;, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L451' href='#L451'><pre>451</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L452' href='#L452'><pre>452</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L453' href='#L453'><pre>453</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L454' href='#L454'><pre>454</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L455' href='#L455'><pre>455</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L456' href='#L456'><pre>456</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource(name, res_type, version)</span></pre></td></tr><tr><td class='line-number'><a name='L457' href='#L457'><pre>457</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L458' href='#L458'><pre>458</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L459' href='#L459'><pre>459</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 获取指定类型的资源安装路径</pre></td></tr><tr><td class='line-number'><a name='L460' href='#L460'><pre>460</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @param resType 资源类型</pre></td></tr><tr><td class='line-number'><a name='L461' href='#L461'><pre>461</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 资源安装路径</pre></td></tr><tr><td class='line-number'><a name='L462' href='#L462'><pre>462</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L463' href='#L463'><pre>463</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn get_resource_install_path(</span></pre></td></tr><tr><td class='line-number'><a name='L464' href='#L464'><pre>464</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L465' href='#L465'><pre>465</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type: ResourceType,</span></pre></td></tr><tr><td class='line-number'><a name='L466' href='#L466'><pre>466</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;String, ResourceError&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L467' href='#L467'><pre>467</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        let <span class='region red'>result</span> = <span class='region red'>ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L468' href='#L468'><pre>468</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_directory()</span></pre></td></tr><tr><td class='line-number'><a name='L469' href='#L469'><pre>469</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource_path_by_type(res_type)</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L470' href='#L470'><pre>470</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>Ok(result)</span></pre></td></tr><tr><td class='line-number'><a name='L471' href='#L471'><pre>471</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L472' href='#L472'><pre>472</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    /**</pre></td></tr><tr><td class='line-number'><a name='L473' href='#L473'><pre>473</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 清理本地资源，清理本地资源会清理本地所有资源，包括未安装的资源,如有忽略资源信息，会被保护，不会被清理</pre></td></tr><tr><td class='line-number'><a name='L474' href='#L474'><pre>474</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * 异步清理，不会阻塞线程</pre></td></tr><tr><td class='line-number'><a name='L475' href='#L475'><pre>475</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     * @return 错误信息</pre></td></tr><tr><td class='line-number'><a name='L476' href='#L476'><pre>476</pre></a></td><td class='skipped-line'></td><td class='code'><pre>     */</pre></td></tr><tr><td class='line-number'><a name='L477' href='#L477'><pre>477</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub async fn clean_local_resources(</span></pre></td></tr><tr><td class='line-number'><a name='L478' href='#L478'><pre>478</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L479' href='#L479'><pre>479</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ignore_res: Vec&lt;ResourceInfo&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L480' href='#L480'><pre>480</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        delete_temp: bool,</span></pre></td></tr><tr><td class='line-number'><a name='L481' href='#L481'><pre>481</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;bool, ResourceError&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L482' href='#L482'><pre>482</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        if self.is_cleaning()</span> {</pre></td></tr><tr><td class='line-number'><a name='L483' href='#L483'><pre>483</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>Err(ResourceError::CacheIsBeingCleaned)</span>;</pre></td></tr><tr><td class='line-number'><a name='L484' href='#L484'><pre>484</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L485' href='#L485'><pre>485</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L486' href='#L486'><pre>486</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_clean_handler()</span></pre></td></tr><tr><td class='line-number'><a name='L487' href='#L487'><pre>487</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .clean(ignore_res, delete_temp)</span></pre></td></tr><tr><td class='line-number'><a name='L488' href='#L488'><pre>488</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            .<span class='region red'>await</span></pre></td></tr><tr><td class='line-number'><a name='L489' href='#L489'><pre>489</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L490' href='#L490'><pre>490</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>pub fn update_resource_to_database(</span></pre></td></tr><tr><td class='line-number'><a name='L491' href='#L491'><pre>491</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self,</span></pre></td></tr><tr><td class='line-number'><a name='L492' href='#L492'><pre>492</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_info: &amp;ResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L493' href='#L493'><pre>493</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;bool, ResourceError&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L494' href='#L494'><pre>494</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L495' href='#L495'><pre>495</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_repository()</span></pre></td></tr><tr><td class='line-number'><a name='L496' href='#L496'><pre>496</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .update_resource(resource_info)</span></pre></td></tr><tr><td class='line-number'><a name='L497' href='#L497'><pre>497</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|_| <span class='region red'>true</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L498' href='#L498'><pre>498</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map_err(ResourceError::DatabaseError)</span></pre></td></tr><tr><td class='line-number'><a name='L499' href='#L499'><pre>499</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L500' href='#L500'><pre>500</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_cleaning(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L501' href='#L501'><pre>501</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L502' href='#L502'><pre>502</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_clean_handler()</span></pre></td></tr><tr><td class='line-number'><a name='L503' href='#L503'><pre>503</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .is_cleaning()</span></pre></td></tr><tr><td class='line-number'><a name='L504' href='#L504'><pre>504</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L505' href='#L505'><pre>505</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr></table></div></body></html>