<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../../style.css'><script src='../../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/resource_rust/rust_resource/src/features/flat/cross_platform.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use super::resource_generated::com::haier::uhome::uplus::rust::resource::fbs::{</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    FBSResourceDownloadPriority, FBSResourceInfo, FBSResourceInfoArgs,</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    FBSResourceInfoListResultCallBack, FBSResourceInfoListResultCallBackArgs,</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    FBSResourceInfoOnProgressResultCallBack, FBSResourceInfoOnProgressResultCallBackArgs,</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    FBSResourceInfoResultCallBack, FBSResourceInfoResultCallBackArgs, FBSResourceType,</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    ResourceContainer, ResourceFlat, ResourceFlatArgs, StrWrapper, StrWrapperArgs,</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>};</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::{</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    api::{</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        error::ResourceError,</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        resource::{ResourcePlatform, ResourceRequestEnvironment},</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        resource_callback::{ResourceCallback, ResourceListCallback},</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        resource_manager::ResourceManager,</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    },</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    cache::database::ResourceDatabaseExecutor,</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    features::flat::resource_generated::com::haier::uhome::uplus::rust::resource::fbs::{</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        BoolWrapper, BoolWrapperArgs, FBSResourceInfoList, FBSResourceInfoListArgs, NoneWrapper,</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        NoneWrapperArgs,</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    },</pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    models::{</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        condition::DeviceCondition,</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        resource_info::{ResourceDownloadPriority, ResourceInfo, ResourceType},</pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    },</pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='skipped-line'></td><td class='code'><pre>};</pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use flatbuffers::{FlatBufferBuilder, UnionWIPOffset, WIPOffset};</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use log::error;</pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use log::{debug, info, warn};</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::collections::HashMap;</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use task_manager::platform::function::PlatformConsumer;</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre>static EMPTY: String = String::new();</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='skipped-line'></td><td class='code'><pre>const ACTION: &amp;str = &quot;action&quot;;</pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='skipped-line'></td><td class='code'><pre>const DEFAULT_SIZE: usize = 1024;</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='skipped-line'></td><td class='code'><pre>const SUCCESS_CODE: i32 = 000000;</pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='skipped-line'></td><td class='code'><pre>macro_rules! require_params {</pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    ($params:expr, $($param:expr),+) =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        for &amp;param in &amp;[$($param),+] {</pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            if !$params.contains_key(param) {</pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                 warn!(&quot;Required parameter &apos;{}&apos; is missing&quot;, param);</pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                return invalid_arg_result(&amp;format!(&quot;{} is required&quot;, param));</pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn lib_resource_cross_platform(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let action = params.get(ACTION).unwrap_or(&amp;EMPTY).as_str();</span></pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    info!</span>(<span class='region red'>&quot;Executing action: {}&quot;</span>, action);</pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    match <span class='region red'>action {</span></pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &quot;init&quot;</span> =&gt; <span class='region red'>init(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;preset_resource&quot;</span> =&gt; <span class='region red'>preset_resource(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;search_normal_resource_list&quot;</span> =&gt; <span class='region red'>search_normal_resource_list(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;search_device_resource_list&quot;</span> =&gt; <span class='region red'>search_device_resource_list(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;cancel&quot;</span> =&gt; <span class='region red'>cancel(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;get_latest_resource&quot;</span> =&gt; <span class='region red'>get_latest_resource(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;get_latest_installed_resource&quot;</span> =&gt; <span class='region red'>get_latest_installed_resource(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;get_resource&quot;</span> =&gt; <span class='region red'>get_resource(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;get_resource_install_path&quot;</span> =&gt; <span class='region red'>get_resource_install_path(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;get_entire_resource_list&quot;</span> =&gt; <span class='region red'>get_entire_resource_list()</span>,</pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;update_resource_to_database&quot;</span> =&gt; <span class='region red'>update_resource_to_database(params)</span>,</pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>warn!</span>(<span class='region red'>&quot;Unsupported action: {}&quot;</span>, action);</pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>invalid_arg_result(&quot;unsupported action&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn lib_resource_cross_platform_consumer_data(</span></pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    params: HashMap&lt;String, String&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    consumer: impl PlatformConsumer + &apos;static,</span></pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let action = params.get(ACTION).unwrap_or(&amp;EMPTY).as_str();</span></pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match action {</span></pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &quot;preset_resource_list&quot;</span> =&gt; <span class='region red'>preset_resource_list(params, consumer)</span>,</pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;query_and_install_normal_resource&quot;</span> =&gt; <span class='region red'>query_and_install_normal_resource(params, consumer)</span>,</pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;query_and_install_device_resource&quot;</span> =&gt; <span class='region red'>query_and_install_device_resource(params, consumer)</span>,</pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;install&quot;</span> =&gt; <span class='region red'>install(params, consumer)</span>,</pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;uninstall&quot;</span> =&gt; <span class='region red'>uninstall(params, consumer)</span>,</pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>warn!</span>(<span class='region red'>&quot;Unsupported action: {}&quot;</span>, action);</pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>invalid_arg_result(&quot;unsupported action&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub async fn lib_resource_cross_platform_async(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let action = params.get(ACTION).unwrap_or(&amp;EMPTY).as_str();</span></pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match action {</span></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &quot;request_normal_resource_list&quot;</span> =&gt; <span class='region red'>request_normal_resource_list(params)</span>.<span class='region red'>await</span>,</pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;request_device_resource_list&quot;</span> =&gt; <span class='region red'>request_device_resource_list(params)</span>.<span class='region red'>await</span>,</pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;clean_local_resources&quot;</span> =&gt; <span class='region red'>clean_local_resources(params)</span>.<span class='region red'>await</span>,</pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>warn!</span>(<span class='region red'>&quot;Unsupported action: {}&quot;</span>, action);</pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>invalid_arg_result(&quot;unsupported action&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn preset_resource_list(</span></pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    params: HashMap&lt;String, String&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    consumer: impl PlatformConsumer + &apos;static,</span></pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;preset_bundle_dir&quot;);</pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>preset_bundle_dir = params[&quot;preset_bundle_dir&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let resource_list_callback = Box::new(CrossPlatformCallback::new(consumer));</span></pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .preset_resource_list(preset_bundle_dir, Some(resource_list_callback))</span></pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        Ok(_) =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>info!</span>(<span class='region red'>&quot;preset_resource_list success&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>success_result()</span></pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;preset_resource_list Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn query_and_install_normal_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    params: HashMap&lt;String, String&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    consumer: impl PlatformConsumer + &apos;static,</span></pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;name&quot;, &quot;res_type&quot;);</pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let name = params[&quot;name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let resource_callback = Box::new(CrossPlatformCallback::new(consumer));</span></pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .query_and_install_normal_resource(name, res_type, Some(resource_callback));</span></pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    success_result()</span></pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn query_and_install_device_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    params: HashMap&lt;String, String&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    consumer: impl PlatformConsumer + &apos;static,</span></pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(</pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>params</span>,</pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;pid&quot;,</pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;res_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;model&quot;,</pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;type_id&quot;,</pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;prod_no&quot;,</pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_net_type&quot;</pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    );</pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>device_condition = get_device_condition(params);</span></pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let resource_callback = Box::new(CrossPlatformCallback::new(consumer));</span></pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .query_and_install_device_resource(device_condition, Some(resource_callback));</span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    success_result()</span></pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn install(params: HashMap&lt;String, String&gt;, consumer: impl PlatformConsumer + &apos;static) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;, &quot;res_name&quot;, &quot;res_version&quot;);</pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_name = params[&quot;res_name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_version = params[&quot;res_version&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let download_priority = params</span></pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get(&quot;download_priority&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .map(</span>|value| <span class='region red'>match value.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &quot;1&quot;</span> =&gt; <span class='region red'>ResourceDownloadPriority::High</span>,</pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            _ =&gt; <span class='region red'>ResourceDownloadPriority::Normal</span>,</pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>}</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .unwrap_or(ResourceDownloadPriority::Normal);</span></pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let resource_callback = Box::new(CrossPlatformCallback::new(consumer))</span>;</pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>mut resource_info</span> = match <span class='region red'>ResourceManager::get_instance().get_resource().get_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_name,</span></pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_type,</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_version,</span></pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    )</span> {</pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(Some(<span class='region red'>info)) =&gt; info</span>,</pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(None) =&gt; return <span class='region red'>failure_result(&quot;Resource info not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>error) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            return failure_result(&amp;format!(&quot;Failed to get resource info: {}&quot;, error), 100)</span></pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>resource_info.download_priority = download_priority;</span></pre></td></tr><tr><td class='line-number'><a name='L179' href='#L179'><pre>179</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L180' href='#L180'><pre>180</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L181' href='#L181'><pre>181</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .install(resource_info, Some(resource_callback))</span></pre></td></tr><tr><td class='line-number'><a name='L182' href='#L182'><pre>182</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L183' href='#L183'><pre>183</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>task_id) =&gt; string_result(task_id)</span>,</pre></td></tr><tr><td class='line-number'><a name='L184' href='#L184'><pre>184</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L185' href='#L185'><pre>185</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L186' href='#L186'><pre>186</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;install Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L187' href='#L187'><pre>187</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L188' href='#L188'><pre>188</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L189' href='#L189'><pre>189</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L190' href='#L190'><pre>190</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn uninstall(</span></pre></td></tr><tr><td class='line-number'><a name='L191' href='#L191'><pre>191</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    params: HashMap&lt;String, String&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L192' href='#L192'><pre>192</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    consumer: impl PlatformConsumer + &apos;static,</span></pre></td></tr><tr><td class='line-number'><a name='L193' href='#L193'><pre>193</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L194' href='#L194'><pre>194</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;, &quot;res_name&quot;, &quot;res_version&quot;);</pre></td></tr><tr><td class='line-number'><a name='L195' href='#L195'><pre>195</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L196' href='#L196'><pre>196</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_name = params[&quot;res_name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L197' href='#L197'><pre>197</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_version = params[&quot;res_version&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L198' href='#L198'><pre>198</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L199' href='#L199'><pre>199</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let resource_callback = Box::new(CrossPlatformCallback::new(consumer))</span>;</pre></td></tr><tr><td class='line-number'><a name='L200' href='#L200'><pre>200</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>resource_info</span> = match <span class='region red'>ResourceManager::get_instance().get_resource().get_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L201' href='#L201'><pre>201</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_name,</span></pre></td></tr><tr><td class='line-number'><a name='L202' href='#L202'><pre>202</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_type,</span></pre></td></tr><tr><td class='line-number'><a name='L203' href='#L203'><pre>203</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_version,</span></pre></td></tr><tr><td class='line-number'><a name='L204' href='#L204'><pre>204</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    )</span> {</pre></td></tr><tr><td class='line-number'><a name='L205' href='#L205'><pre>205</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(Some(<span class='region red'>info)) =&gt; info</span>,</pre></td></tr><tr><td class='line-number'><a name='L206' href='#L206'><pre>206</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(None) =&gt; return <span class='region red'>failure_result(&quot;Resource info not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L207' href='#L207'><pre>207</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>error) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L208' href='#L208'><pre>208</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            return failure_result(&amp;format!(&quot;Failed to get resource info: {}&quot;, error), 100)</span></pre></td></tr><tr><td class='line-number'><a name='L209' href='#L209'><pre>209</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L210' href='#L210'><pre>210</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L211' href='#L211'><pre>211</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    match <span class='region red'>ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L212' href='#L212'><pre>212</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L213' href='#L213'><pre>213</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .uninstall(resource_info, Some(resource_callback))</span></pre></td></tr><tr><td class='line-number'><a name='L214' href='#L214'><pre>214</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L215' href='#L215'><pre>215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>task_id) =&gt; string_result(task_id)</span>,</pre></td></tr><tr><td class='line-number'><a name='L216' href='#L216'><pre>216</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L217' href='#L217'><pre>217</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L218' href='#L218'><pre>218</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;uninstall Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L219' href='#L219'><pre>219</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L220' href='#L220'><pre>220</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L221' href='#L221'><pre>221</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L222' href='#L222'><pre>222</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L223' href='#L223'><pre>223</pre></a></td><td class='skipped-line'></td><td class='code'><pre>struct CrossPlatformCallback {</pre></td></tr><tr><td class='line-number'><a name='L224' href='#L224'><pre>224</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    consumer: Box&lt;dyn PlatformConsumer&gt;,</pre></td></tr><tr><td class='line-number'><a name='L225' href='#L225'><pre>225</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L226' href='#L226'><pre>226</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L227' href='#L227'><pre>227</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl CrossPlatformCallback {</pre></td></tr><tr><td class='line-number'><a name='L228' href='#L228'><pre>228</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn new(consumer: impl PlatformConsumer + &apos;static) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L229' href='#L229'><pre>229</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Self {</span></pre></td></tr><tr><td class='line-number'><a name='L230' href='#L230'><pre>230</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            consumer: Box::new(consumer),</span></pre></td></tr><tr><td class='line-number'><a name='L231' href='#L231'><pre>231</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L232' href='#L232'><pre>232</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L233' href='#L233'><pre>233</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L234' href='#L234'><pre>234</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L235' href='#L235'><pre>235</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl ResourceListCallback for CrossPlatformCallback {</pre></td></tr><tr><td class='line-number'><a name='L236' href='#L236'><pre>236</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn on_list_result(&amp;self, resources: &amp;Vec&lt;ResourceInfo&gt;, error_message: Option&lt;String&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L237' href='#L237'><pre>237</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L238' href='#L238'><pre>238</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L239' href='#L239'><pre>239</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L240' href='#L240'><pre>240</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L241' href='#L241'><pre>241</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L242' href='#L242'><pre>242</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L243' href='#L243'><pre>243</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_args = FBSResourceInfoListResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L244' href='#L244'><pre>244</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            info_list: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L245' href='#L245'><pre>245</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            message: error_message.as_ref().map(</span>|it| <span class='region red'>builder.create_string(it)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L246' href='#L246'><pre>246</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        };</span></pre></td></tr><tr><td class='line-number'><a name='L247' href='#L247'><pre>247</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_result_callback =</span></pre></td></tr><tr><td class='line-number'><a name='L248' href='#L248'><pre>248</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            FBSResourceInfoListResultCallBack::create(&amp;mut builder, &amp;res_args);</span></pre></td></tr><tr><td class='line-number'><a name='L249' href='#L249'><pre>249</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let data = data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L250' href='#L250'><pre>250</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L251' href='#L251'><pre>251</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            res_result_callback.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L252' href='#L252'><pre>252</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ResourceContainer::FBSResourceInfoListResultCallBack,</span></pre></td></tr><tr><td class='line-number'><a name='L253' href='#L253'><pre>253</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        );</span></pre></td></tr><tr><td class='line-number'><a name='L254' href='#L254'><pre>254</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.consumer.accept(data);</span></pre></td></tr><tr><td class='line-number'><a name='L255' href='#L255'><pre>255</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L256' href='#L256'><pre>256</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn unique_id(&amp;self) -&gt; &amp;str {</span></pre></td></tr><tr><td class='line-number'><a name='L257' href='#L257'><pre>257</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.consumer.unique_id()</span></pre></td></tr><tr><td class='line-number'><a name='L258' href='#L258'><pre>258</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L259' href='#L259'><pre>259</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L260' href='#L260'><pre>260</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl ResourceCallback for CrossPlatformCallback {</pre></td></tr><tr><td class='line-number'><a name='L261' href='#L261'><pre>261</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn on_progress_changed(&amp;self, resource_info: &amp;ResourceInfo, progress: usize) {</span></pre></td></tr><tr><td class='line-number'><a name='L262' href='#L262'><pre>262</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L263' href='#L263'><pre>263</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let fbs_resource = create_fbs_resource_info(&amp;mut builder, resource_info);</span></pre></td></tr><tr><td class='line-number'><a name='L264' href='#L264'><pre>264</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_args = FBSResourceInfoOnProgressResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L265' href='#L265'><pre>265</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            info: Some(fbs_resource),</span></pre></td></tr><tr><td class='line-number'><a name='L266' href='#L266'><pre>266</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            progress: progress as i32,</span></pre></td></tr><tr><td class='line-number'><a name='L267' href='#L267'><pre>267</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        };</span></pre></td></tr><tr><td class='line-number'><a name='L268' href='#L268'><pre>268</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_result_callback =</span></pre></td></tr><tr><td class='line-number'><a name='L269' href='#L269'><pre>269</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            FBSResourceInfoOnProgressResultCallBack::create(&amp;mut builder, &amp;res_args);</span></pre></td></tr><tr><td class='line-number'><a name='L270' href='#L270'><pre>270</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let data = data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L271' href='#L271'><pre>271</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L272' href='#L272'><pre>272</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            res_result_callback.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L273' href='#L273'><pre>273</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ResourceContainer::FBSResourceInfoOnProgressResultCallBack,</span></pre></td></tr><tr><td class='line-number'><a name='L274' href='#L274'><pre>274</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        );</span></pre></td></tr><tr><td class='line-number'><a name='L275' href='#L275'><pre>275</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.consumer.accept(data);</span></pre></td></tr><tr><td class='line-number'><a name='L276' href='#L276'><pre>276</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L277' href='#L277'><pre>277</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn on_result(&amp;self, resource: &amp;ResourceInfo, error_message: Option&lt;String&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L278' href='#L278'><pre>278</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L279' href='#L279'><pre>279</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let install_resource = ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L280' href='#L280'><pre>280</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L281' href='#L281'><pre>281</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource(&amp;resource.name, &amp;resource.resource_type, &amp;resource.version)</span></pre></td></tr><tr><td class='line-number'><a name='L282' href='#L282'><pre>282</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .ok()</span></pre></td></tr><tr><td class='line-number'><a name='L283' href='#L283'><pre>283</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .and_then(</span>|r| <span class='region red'>r</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L284' href='#L284'><pre>284</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .unwrap_or_else(</span>|| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L285' href='#L285'><pre>285</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                debug!</span>(<span class='region red'>&quot;Using original resource info as fallback&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L286' href='#L286'><pre>286</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>resource.clone()</span></pre></td></tr><tr><td class='line-number'><a name='L287' href='#L287'><pre>287</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='region red'>);</span></pre></td></tr><tr><td class='line-number'><a name='L288' href='#L288'><pre>288</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let fbs_resource = create_fbs_resource_info(&amp;mut builder, &amp;install_resource);</span></pre></td></tr><tr><td class='line-number'><a name='L289' href='#L289'><pre>289</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_args = FBSResourceInfoResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L290' href='#L290'><pre>290</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            info: Some(fbs_resource),</span></pre></td></tr><tr><td class='line-number'><a name='L291' href='#L291'><pre>291</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            message: error_message.as_ref().map(</span>|s| <span class='region red'>builder.create_string(s)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L292' href='#L292'><pre>292</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        };</span></pre></td></tr><tr><td class='line-number'><a name='L293' href='#L293'><pre>293</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let res_result_callback = FBSResourceInfoResultCallBack::create(&amp;mut builder, &amp;res_args);</span></pre></td></tr><tr><td class='line-number'><a name='L294' href='#L294'><pre>294</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let data = data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L295' href='#L295'><pre>295</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L296' href='#L296'><pre>296</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            res_result_callback.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L297' href='#L297'><pre>297</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            ResourceContainer::FBSResourceInfoResultCallBack,</span></pre></td></tr><tr><td class='line-number'><a name='L298' href='#L298'><pre>298</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        );</span></pre></td></tr><tr><td class='line-number'><a name='L299' href='#L299'><pre>299</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.consumer.accept(data);</span></pre></td></tr><tr><td class='line-number'><a name='L300' href='#L300'><pre>300</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L301' href='#L301'><pre>301</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn unique_id(&amp;self) -&gt; &amp;str {</span></pre></td></tr><tr><td class='line-number'><a name='L302' href='#L302'><pre>302</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.consumer.unique_id()</span></pre></td></tr><tr><td class='line-number'><a name='L303' href='#L303'><pre>303</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L304' href='#L304'><pre>304</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L305' href='#L305'><pre>305</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L306' href='#L306'><pre>306</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>async fn request_normal_resource_list(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L307' href='#L307'><pre>307</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;name&quot;, &quot;res_type&quot;);</pre></td></tr><tr><td class='line-number'><a name='L308' href='#L308'><pre>308</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L309' href='#L309'><pre>309</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let name = params[&quot;name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L310' href='#L310'><pre>310</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L311' href='#L311'><pre>311</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L312' href='#L312'><pre>312</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L313' href='#L313'><pre>313</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .request_normal_resource_list(name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L314' href='#L314'><pre>314</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        .<span class='region red'>await</span></pre></td></tr><tr><td class='line-number'><a name='L315' href='#L315'><pre>315</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L316' href='#L316'><pre>316</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resources) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L317' href='#L317'><pre>317</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L318' href='#L318'><pre>318</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L319' href='#L319'><pre>319</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L320' href='#L320'><pre>320</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L321' href='#L321'><pre>321</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L322' href='#L322'><pre>322</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L323' href='#L323'><pre>323</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures_args = FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L324' href='#L324'><pre>324</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                resources: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L325' href='#L325'><pre>325</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            };</span></pre></td></tr><tr><td class='line-number'><a name='L326' href='#L326'><pre>326</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoure_list = FBSResourceInfoList::create(&amp;mut builder, &amp;fbs_resoures_args);</span></pre></td></tr><tr><td class='line-number'><a name='L327' href='#L327'><pre>327</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L328' href='#L328'><pre>328</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L329' href='#L329'><pre>329</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                fbs_resoure_list.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L330' href='#L330'><pre>330</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::FBSResourceInfoList,</span></pre></td></tr><tr><td class='line-number'><a name='L331' href='#L331'><pre>331</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L332' href='#L332'><pre>332</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L333' href='#L333'><pre>333</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L334' href='#L334'><pre>334</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L335' href='#L335'><pre>335</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;request failed:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L336' href='#L336'><pre>336</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L337' href='#L337'><pre>337</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L338' href='#L338'><pre>338</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L339' href='#L339'><pre>339</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L340' href='#L340'><pre>340</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>async fn request_device_resource_list(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L341' href='#L341'><pre>341</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(</pre></td></tr><tr><td class='line-number'><a name='L342' href='#L342'><pre>342</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>params</span>,</pre></td></tr><tr><td class='line-number'><a name='L343' href='#L343'><pre>343</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;pid&quot;,</pre></td></tr><tr><td class='line-number'><a name='L344' href='#L344'><pre>344</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;res_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L345' href='#L345'><pre>345</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;model&quot;,</pre></td></tr><tr><td class='line-number'><a name='L346' href='#L346'><pre>346</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;type_id&quot;,</pre></td></tr><tr><td class='line-number'><a name='L347' href='#L347'><pre>347</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;prod_no&quot;,</pre></td></tr><tr><td class='line-number'><a name='L348' href='#L348'><pre>348</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L349' href='#L349'><pre>349</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_net_type&quot;</pre></td></tr><tr><td class='line-number'><a name='L350' href='#L350'><pre>350</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    );</pre></td></tr><tr><td class='line-number'><a name='L351' href='#L351'><pre>351</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>device_condition = get_device_condition(params);</span></pre></td></tr><tr><td class='line-number'><a name='L352' href='#L352'><pre>352</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L353' href='#L353'><pre>353</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L354' href='#L354'><pre>354</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .request_device_resource_list(device_condition)</span></pre></td></tr><tr><td class='line-number'><a name='L355' href='#L355'><pre>355</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        .<span class='region red'>await</span></pre></td></tr><tr><td class='line-number'><a name='L356' href='#L356'><pre>356</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L357' href='#L357'><pre>357</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resources) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L358' href='#L358'><pre>358</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L359' href='#L359'><pre>359</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L360' href='#L360'><pre>360</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L361' href='#L361'><pre>361</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L362' href='#L362'><pre>362</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L363' href='#L363'><pre>363</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L364' href='#L364'><pre>364</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures_args = FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L365' href='#L365'><pre>365</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                resources: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L366' href='#L366'><pre>366</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            };</span></pre></td></tr><tr><td class='line-number'><a name='L367' href='#L367'><pre>367</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoure_list = FBSResourceInfoList::create(&amp;mut builder, &amp;fbs_resoures_args);</span></pre></td></tr><tr><td class='line-number'><a name='L368' href='#L368'><pre>368</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L369' href='#L369'><pre>369</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L370' href='#L370'><pre>370</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                fbs_resoure_list.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L371' href='#L371'><pre>371</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::FBSResourceInfoList,</span></pre></td></tr><tr><td class='line-number'><a name='L372' href='#L372'><pre>372</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L373' href='#L373'><pre>373</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L374' href='#L374'><pre>374</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L375' href='#L375'><pre>375</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L376' href='#L376'><pre>376</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;request failed:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L377' href='#L377'><pre>377</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L378' href='#L378'><pre>378</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L379' href='#L379'><pre>379</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L380' href='#L380'><pre>380</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>async fn clean_local_resources(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; </span><span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L381' href='#L381'><pre>381</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;keep_resource_json&quot;);</pre></td></tr><tr><td class='line-number'><a name='L382' href='#L382'><pre>382</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>keep_resource_json = params[&quot;keep_resource_json&quot;].to_string()</span>;</pre></td></tr><tr><td class='line-number'><a name='L383' href='#L383'><pre>383</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>keep_resource_map_list: Vec&lt;HashMap&lt;String, String&gt;&gt;</span> =</pre></td></tr><tr><td class='line-number'><a name='L384' href='#L384'><pre>384</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        match <span class='region red'>serde_json::from_str(&amp;keep_resource_json)</span> {</pre></td></tr><tr><td class='line-number'><a name='L385' href='#L385'><pre>385</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Ok(<span class='region red'>list) =&gt; list</span>,</pre></td></tr><tr><td class='line-number'><a name='L386' href='#L386'><pre>386</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Err(<span class='region red'>e) =&gt; return failure_result(&amp;format!(&quot;not find resource_info:{}&quot;, e), 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L387' href='#L387'><pre>387</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        };</pre></td></tr><tr><td class='line-number'><a name='L388' href='#L388'><pre>388</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>keep_resource_list: Vec&lt;ResourceInfo&gt; = keep_resource_map_list</span></pre></td></tr><tr><td class='line-number'><a name='L389' href='#L389'><pre>389</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L390' href='#L390'><pre>390</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .filter_map(</span>|it| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L391' href='#L391'><pre>391</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let res_type = ResourceType::from(it[&quot;res_type&quot;].to_string());</span></pre></td></tr><tr><td class='line-number'><a name='L392' href='#L392'><pre>392</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let res_name = &amp;it[&quot;res_name&quot;];</span></pre></td></tr><tr><td class='line-number'><a name='L393' href='#L393'><pre>393</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            match it.get(&quot;res_version&quot;)</span> {</pre></td></tr><tr><td class='line-number'><a name='L394' href='#L394'><pre>394</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                Some(<span class='region red'>version</span>) if !<span class='region red'>version</span>.is_empty(<span class='region red'>) =&gt; ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L395' href='#L395'><pre>395</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L396' href='#L396'><pre>396</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .get_resource(res_name, &amp;res_type, version)</span></pre></td></tr><tr><td class='line-number'><a name='L397' href='#L397'><pre>397</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .ok()</span></pre></td></tr><tr><td class='line-number'><a name='L398' href='#L398'><pre>398</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .flatten()</span>,</pre></td></tr><tr><td class='line-number'><a name='L399' href='#L399'><pre>399</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                _ =&gt; <span class='region red'>ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L400' href='#L400'><pre>400</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L401' href='#L401'><pre>401</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .get_latest_installed_resource(res_name.to_string(), res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L402' href='#L402'><pre>402</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .ok()</span></pre></td></tr><tr><td class='line-number'><a name='L403' href='#L403'><pre>403</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    .flatten()</span>,</pre></td></tr><tr><td class='line-number'><a name='L404' href='#L404'><pre>404</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L405' href='#L405'><pre>405</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>}</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L406' href='#L406'><pre>406</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .collect()</span>;</pre></td></tr><tr><td class='line-number'><a name='L407' href='#L407'><pre>407</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>delete_temp</span> = match <span class='region red'>params.get(&quot;delete_temp&quot;)</span> {</pre></td></tr><tr><td class='line-number'><a name='L408' href='#L408'><pre>408</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Some(<span class='region red'>v) =&gt; v.eq_ignore_ascii_case(&quot;true&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L409' href='#L409'><pre>409</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        None =&gt; <span class='region red'>true</span>,</pre></td></tr><tr><td class='line-number'><a name='L410' href='#L410'><pre>410</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L411' href='#L411'><pre>411</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    match <span class='region red'>ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L412' href='#L412'><pre>412</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L413' href='#L413'><pre>413</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .clean_local_resources(keep_resource_list, delete_temp)</span></pre></td></tr><tr><td class='line-number'><a name='L414' href='#L414'><pre>414</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        .<span class='region red'>await</span></pre></td></tr><tr><td class='line-number'><a name='L415' href='#L415'><pre>415</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L416' href='#L416'><pre>416</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>b) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L417' href='#L417'><pre>417</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L418' href='#L418'><pre>418</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let bool_wrapper = BoolWrapper::create(&amp;mut builder, &amp;BoolWrapperArgs { value: b });</span></pre></td></tr><tr><td class='line-number'><a name='L419' href='#L419'><pre>419</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L420' href='#L420'><pre>420</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L421' href='#L421'><pre>421</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                bool_wrapper.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L422' href='#L422'><pre>422</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::BoolWrapper,</span></pre></td></tr><tr><td class='line-number'><a name='L423' href='#L423'><pre>423</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L424' href='#L424'><pre>424</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L425' href='#L425'><pre>425</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L426' href='#L426'><pre>426</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L427' href='#L427'><pre>427</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;clean failed:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L428' href='#L428'><pre>428</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L429' href='#L429'><pre>429</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L430' href='#L430'><pre>430</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L431' href='#L431'><pre>431</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn init(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L432' href='#L432'><pre>432</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(</pre></td></tr><tr><td class='line-number'><a name='L433' href='#L433'><pre>433</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>params</span>,</pre></td></tr><tr><td class='line-number'><a name='L434' href='#L434'><pre>434</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;resource_root_path&quot;,</pre></td></tr><tr><td class='line-number'><a name='L435' href='#L435'><pre>435</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;platform&quot;,</pre></td></tr><tr><td class='line-number'><a name='L436' href='#L436'><pre>436</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;app_version&quot;,</pre></td></tr><tr><td class='line-number'><a name='L437' href='#L437'><pre>437</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;request_environment&quot;,</pre></td></tr><tr><td class='line-number'><a name='L438' href='#L438'><pre>438</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;test_mode&quot;</pre></td></tr><tr><td class='line-number'><a name='L439' href='#L439'><pre>439</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    );</pre></td></tr><tr><td class='line-number'><a name='L440' href='#L440'><pre>440</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>app_version = params[&quot;app_version&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L441' href='#L441'><pre>441</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut resource_root_path = params[&quot;resource_root_path&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L442' href='#L442'><pre>442</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let test_mode = params[&quot;test_mode&quot;].eq_ignore_ascii_case(&quot;true&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L443' href='#L443'><pre>443</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if test_mode</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L444' href='#L444'><pre>444</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_root_path.push_str(&quot;-test&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L445' href='#L445'><pre>445</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L446' href='#L446'><pre>446</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>platform</span> = match <span class='region red'>params[&quot;platform&quot;].parse().unwrap_or(0)</span> {</pre></td></tr><tr><td class='line-number'><a name='L447' href='#L447'><pre>447</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        0 =&gt; <span class='region red'>ResourcePlatform::ResourceAppPlatformChina</span>,</pre></td></tr><tr><td class='line-number'><a name='L448' href='#L448'><pre>448</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        1 =&gt; <span class='region red'>ResourcePlatform::ResourceAppPlatformSoutheastAsia</span>,</pre></td></tr><tr><td class='line-number'><a name='L449' href='#L449'><pre>449</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>ResourcePlatform::Unknown</span>,</pre></td></tr><tr><td class='line-number'><a name='L450' href='#L450'><pre>450</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L451' href='#L451'><pre>451</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>request_environment</span> = match <span class='region red'>params[&quot;request_environment&quot;].parse().unwrap_or(0)</span> {</pre></td></tr><tr><td class='line-number'><a name='L452' href='#L452'><pre>452</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        0 =&gt; <span class='region red'>ResourceRequestEnvironment::RequestEnvProduction</span>,</pre></td></tr><tr><td class='line-number'><a name='L453' href='#L453'><pre>453</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        1 =&gt; <span class='region red'>ResourceRequestEnvironment::RequestEnvAcceptance</span>,</pre></td></tr><tr><td class='line-number'><a name='L454' href='#L454'><pre>454</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>ResourceRequestEnvironment::Unknown</span>,</pre></td></tr><tr><td class='line-number'><a name='L455' href='#L455'><pre>455</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L456' href='#L456'><pre>456</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>database</span> = match <span class='region red'>ResourceDatabaseExecutor::new(&amp;resource_root_path)</span> {</pre></td></tr><tr><td class='line-number'><a name='L457' href='#L457'><pre>457</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>database) =&gt; database</span>,</pre></td></tr><tr><td class='line-number'><a name='L458' href='#L458'><pre>458</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>error) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L459' href='#L459'><pre>459</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            error!</span>(<span class='region red'>&quot;Failed to initialize database: {}&quot;</span>, error);</pre></td></tr><tr><td class='line-number'><a name='L460' href='#L460'><pre>460</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            return <span class='region red'>failure_result(&quot;Failed to initialize database&quot;, 900001)</span>;</pre></td></tr><tr><td class='line-number'><a name='L461' href='#L461'><pre>461</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L462' href='#L462'><pre>462</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    };</pre></td></tr><tr><td class='line-number'><a name='L463' href='#L463'><pre>463</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>ResourceManager::get_instance().init_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L464' href='#L464'><pre>464</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_root_path,</span></pre></td></tr><tr><td class='line-number'><a name='L465' href='#L465'><pre>465</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        platform,</span></pre></td></tr><tr><td class='line-number'><a name='L466' href='#L466'><pre>466</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        app_version,</span></pre></td></tr><tr><td class='line-number'><a name='L467' href='#L467'><pre>467</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        request_environment,</span></pre></td></tr><tr><td class='line-number'><a name='L468' href='#L468'><pre>468</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        test_mode,</span></pre></td></tr><tr><td class='line-number'><a name='L469' href='#L469'><pre>469</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Box::new(database),</span></pre></td></tr><tr><td class='line-number'><a name='L470' href='#L470'><pre>470</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    );</span></pre></td></tr><tr><td class='line-number'><a name='L471' href='#L471'><pre>471</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    success_result()</span></pre></td></tr><tr><td class='line-number'><a name='L472' href='#L472'><pre>472</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L473' href='#L473'><pre>473</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L474' href='#L474'><pre>474</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn preset_resource(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L475' href='#L475'><pre>475</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;name&quot;, &quot;res_type&quot;, &quot;preset_bundle_dir&quot;);</pre></td></tr><tr><td class='line-number'><a name='L476' href='#L476'><pre>476</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L477' href='#L477'><pre>477</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let name = params[&quot;name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L478' href='#L478'><pre>478</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L479' href='#L479'><pre>479</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let preset_bundle_dir = params[&quot;preset_bundle_dir&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L480' href='#L480'><pre>480</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L481' href='#L481'><pre>481</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L482' href='#L482'><pre>482</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .preset_resource(name, res_type, preset_bundle_dir)</span></pre></td></tr><tr><td class='line-number'><a name='L483' href='#L483'><pre>483</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L484' href='#L484'><pre>484</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>res) =&gt; match res</span> {</pre></td></tr><tr><td class='line-number'><a name='L485' href='#L485'><pre>485</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Some(<span class='region red'>resource_info) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L486' href='#L486'><pre>486</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L487' href='#L487'><pre>487</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let fbs_resource = create_fbs_resource_info(&amp;mut builder, &amp;resource_info);</span></pre></td></tr><tr><td class='line-number'><a name='L488' href='#L488'><pre>488</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L489' href='#L489'><pre>489</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L490' href='#L490'><pre>490</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    fbs_resource.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L491' href='#L491'><pre>491</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    ResourceContainer::FBSResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L492' href='#L492'><pre>492</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                )</span></pre></td></tr><tr><td class='line-number'><a name='L493' href='#L493'><pre>493</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L494' href='#L494'><pre>494</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            None =&gt; <span class='region red'>failure_result(&quot;preset_resource Resource not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L495' href='#L495'><pre>495</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L496' href='#L496'><pre>496</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L497' href='#L497'><pre>497</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L498' href='#L498'><pre>498</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;preset_resource Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L499' href='#L499'><pre>499</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L500' href='#L500'><pre>500</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L501' href='#L501'><pre>501</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L502' href='#L502'><pre>502</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L503' href='#L503'><pre>503</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn search_normal_resource_list(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L504' href='#L504'><pre>504</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;name&quot;, &quot;res_type&quot;);</pre></td></tr><tr><td class='line-number'><a name='L505' href='#L505'><pre>505</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L506' href='#L506'><pre>506</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let name = params[&quot;name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L507' href='#L507'><pre>507</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L508' href='#L508'><pre>508</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L509' href='#L509'><pre>509</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L510' href='#L510'><pre>510</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .search_normal_resource_list(name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L511' href='#L511'><pre>511</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L512' href='#L512'><pre>512</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resources) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L513' href='#L513'><pre>513</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L514' href='#L514'><pre>514</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L515' href='#L515'><pre>515</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L516' href='#L516'><pre>516</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L517' href='#L517'><pre>517</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L518' href='#L518'><pre>518</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L519' href='#L519'><pre>519</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures_args = FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L520' href='#L520'><pre>520</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                resources: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L521' href='#L521'><pre>521</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            };</span></pre></td></tr><tr><td class='line-number'><a name='L522' href='#L522'><pre>522</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoure_list = FBSResourceInfoList::create(&amp;mut builder, &amp;fbs_resoures_args);</span></pre></td></tr><tr><td class='line-number'><a name='L523' href='#L523'><pre>523</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L524' href='#L524'><pre>524</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L525' href='#L525'><pre>525</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                fbs_resoure_list.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L526' href='#L526'><pre>526</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::FBSResourceInfoList,</span></pre></td></tr><tr><td class='line-number'><a name='L527' href='#L527'><pre>527</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L528' href='#L528'><pre>528</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L529' href='#L529'><pre>529</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L530' href='#L530'><pre>530</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L531' href='#L531'><pre>531</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;search_normal_resource_list Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L532' href='#L532'><pre>532</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L533' href='#L533'><pre>533</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L534' href='#L534'><pre>534</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L535' href='#L535'><pre>535</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L536' href='#L536'><pre>536</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn search_device_resource_list(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L537' href='#L537'><pre>537</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(</pre></td></tr><tr><td class='line-number'><a name='L538' href='#L538'><pre>538</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>params</span>,</pre></td></tr><tr><td class='line-number'><a name='L539' href='#L539'><pre>539</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;pid&quot;,</pre></td></tr><tr><td class='line-number'><a name='L540' href='#L540'><pre>540</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;res_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L541' href='#L541'><pre>541</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;model&quot;,</pre></td></tr><tr><td class='line-number'><a name='L542' href='#L542'><pre>542</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;type_id&quot;,</pre></td></tr><tr><td class='line-number'><a name='L543' href='#L543'><pre>543</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;prod_no&quot;,</pre></td></tr><tr><td class='line-number'><a name='L544' href='#L544'><pre>544</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_type&quot;,</pre></td></tr><tr><td class='line-number'><a name='L545' href='#L545'><pre>545</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        &quot;device_net_type&quot;</pre></td></tr><tr><td class='line-number'><a name='L546' href='#L546'><pre>546</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    );</pre></td></tr><tr><td class='line-number'><a name='L547' href='#L547'><pre>547</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>device_condition = get_device_condition(params);</span></pre></td></tr><tr><td class='line-number'><a name='L548' href='#L548'><pre>548</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L549' href='#L549'><pre>549</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L550' href='#L550'><pre>550</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .search_device_resource_list(device_condition)</span></pre></td></tr><tr><td class='line-number'><a name='L551' href='#L551'><pre>551</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L552' href='#L552'><pre>552</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resources) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L553' href='#L553'><pre>553</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L554' href='#L554'><pre>554</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L555' href='#L555'><pre>555</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L556' href='#L556'><pre>556</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L557' href='#L557'><pre>557</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L558' href='#L558'><pre>558</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L559' href='#L559'><pre>559</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures_args = FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L560' href='#L560'><pre>560</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                resources: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L561' href='#L561'><pre>561</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            };</span></pre></td></tr><tr><td class='line-number'><a name='L562' href='#L562'><pre>562</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoure_list = FBSResourceInfoList::create(&amp;mut builder, &amp;fbs_resoures_args);</span></pre></td></tr><tr><td class='line-number'><a name='L563' href='#L563'><pre>563</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L564' href='#L564'><pre>564</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L565' href='#L565'><pre>565</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                fbs_resoure_list.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L566' href='#L566'><pre>566</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::FBSResourceInfoList,</span></pre></td></tr><tr><td class='line-number'><a name='L567' href='#L567'><pre>567</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L568' href='#L568'><pre>568</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L569' href='#L569'><pre>569</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L570' href='#L570'><pre>570</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L571' href='#L571'><pre>571</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;search_device_resource_list Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L572' href='#L572'><pre>572</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L573' href='#L573'><pre>573</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L574' href='#L574'><pre>574</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L575' href='#L575'><pre>575</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L576' href='#L576'><pre>576</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn cancel(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L577' href='#L577'><pre>577</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;task_id&quot;);</pre></td></tr><tr><td class='line-number'><a name='L578' href='#L578'><pre>578</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>task_id = params[&quot;task_id&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L579' href='#L579'><pre>579</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L580' href='#L580'><pre>580</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L581' href='#L581'><pre>581</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .cancel(&amp;task_id)</span></pre></td></tr><tr><td class='line-number'><a name='L582' href='#L582'><pre>582</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L583' href='#L583'><pre>583</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(_) =&gt; <span class='region red'>success_result()</span>,</pre></td></tr><tr><td class='line-number'><a name='L584' href='#L584'><pre>584</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L585' href='#L585'><pre>585</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L586' href='#L586'><pre>586</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;cancel Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L587' href='#L587'><pre>587</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L588' href='#L588'><pre>588</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L589' href='#L589'><pre>589</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L590' href='#L590'><pre>590</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_latest_resource(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L591' href='#L591'><pre>591</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;, &quot;res_name&quot;);</pre></td></tr><tr><td class='line-number'><a name='L592' href='#L592'><pre>592</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L593' href='#L593'><pre>593</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_name = params[&quot;res_name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L594' href='#L594'><pre>594</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L595' href='#L595'><pre>595</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L596' href='#L596'><pre>596</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L597' href='#L597'><pre>597</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_latest_resource(res_name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L598' href='#L598'><pre>598</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L599' href='#L599'><pre>599</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>re) =&gt; match re</span> {</pre></td></tr><tr><td class='line-number'><a name='L600' href='#L600'><pre>600</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Some(<span class='region red'>resource_info) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L601' href='#L601'><pre>601</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L602' href='#L602'><pre>602</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let fbs_resource = create_fbs_resource_info(&amp;mut builder, &amp;resource_info);</span></pre></td></tr><tr><td class='line-number'><a name='L603' href='#L603'><pre>603</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L604' href='#L604'><pre>604</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L605' href='#L605'><pre>605</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    fbs_resource.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L606' href='#L606'><pre>606</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    ResourceContainer::FBSResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L607' href='#L607'><pre>607</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                )</span></pre></td></tr><tr><td class='line-number'><a name='L608' href='#L608'><pre>608</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L609' href='#L609'><pre>609</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            None =&gt; <span class='region red'>failure_result(&quot;resource not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L610' href='#L610'><pre>610</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L611' href='#L611'><pre>611</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L612' href='#L612'><pre>612</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L613' href='#L613'><pre>613</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;get_latest_resource Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L614' href='#L614'><pre>614</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L615' href='#L615'><pre>615</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L616' href='#L616'><pre>616</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L617' href='#L617'><pre>617</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_latest_installed_resource(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L618' href='#L618'><pre>618</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;, &quot;res_name&quot;);</pre></td></tr><tr><td class='line-number'><a name='L619' href='#L619'><pre>619</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L620' href='#L620'><pre>620</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_name = params[&quot;res_name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L621' href='#L621'><pre>621</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L622' href='#L622'><pre>622</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L623' href='#L623'><pre>623</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L624' href='#L624'><pre>624</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_latest_installed_resource(res_name, res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L625' href='#L625'><pre>625</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L626' href='#L626'><pre>626</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>re) =&gt; match re</span> {</pre></td></tr><tr><td class='line-number'><a name='L627' href='#L627'><pre>627</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Some(<span class='region red'>resource_info) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L628' href='#L628'><pre>628</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L629' href='#L629'><pre>629</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let fbs_resource = create_fbs_resource_info(&amp;mut builder, &amp;resource_info);</span></pre></td></tr><tr><td class='line-number'><a name='L630' href='#L630'><pre>630</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L631' href='#L631'><pre>631</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L632' href='#L632'><pre>632</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    fbs_resource.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L633' href='#L633'><pre>633</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    ResourceContainer::FBSResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L634' href='#L634'><pre>634</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                )</span></pre></td></tr><tr><td class='line-number'><a name='L635' href='#L635'><pre>635</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L636' href='#L636'><pre>636</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            None =&gt; <span class='region red'>failure_result(&quot;resource not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L637' href='#L637'><pre>637</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L638' href='#L638'><pre>638</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L639' href='#L639'><pre>639</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L640' href='#L640'><pre>640</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(</span></pre></td></tr><tr><td class='line-number'><a name='L641' href='#L641'><pre>641</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;format!(&quot;get_latest_installed_resource Error:{}&quot;, msg),</span></pre></td></tr><tr><td class='line-number'><a name='L642' href='#L642'><pre>642</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                code,</span></pre></td></tr><tr><td class='line-number'><a name='L643' href='#L643'><pre>643</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L644' href='#L644'><pre>644</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L645' href='#L645'><pre>645</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L646' href='#L646'><pre>646</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L647' href='#L647'><pre>647</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_resource(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L648' href='#L648'><pre>648</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;, &quot;res_name&quot;, &quot;res_version&quot;);</pre></td></tr><tr><td class='line-number'><a name='L649' href='#L649'><pre>649</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L650' href='#L650'><pre>650</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_name = params[&quot;res_name&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L651' href='#L651'><pre>651</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_version = params[&quot;res_version&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L652' href='#L652'><pre>652</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L653' href='#L653'><pre>653</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance().get_resource().get_resource(</span></pre></td></tr><tr><td class='line-number'><a name='L654' href='#L654'><pre>654</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_name,</span></pre></td></tr><tr><td class='line-number'><a name='L655' href='#L655'><pre>655</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_type,</span></pre></td></tr><tr><td class='line-number'><a name='L656' href='#L656'><pre>656</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;res_version,</span></pre></td></tr><tr><td class='line-number'><a name='L657' href='#L657'><pre>657</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    )</span> {</pre></td></tr><tr><td class='line-number'><a name='L658' href='#L658'><pre>658</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>re) =&gt; match re</span> {</pre></td></tr><tr><td class='line-number'><a name='L659' href='#L659'><pre>659</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Some(<span class='region red'>resource_info) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L660' href='#L660'><pre>660</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L661' href='#L661'><pre>661</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let fbs_resource = create_fbs_resource_info(&amp;mut builder, &amp;resource_info);</span></pre></td></tr><tr><td class='line-number'><a name='L662' href='#L662'><pre>662</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L663' href='#L663'><pre>663</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L664' href='#L664'><pre>664</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    fbs_resource.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L665' href='#L665'><pre>665</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    ResourceContainer::FBSResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L666' href='#L666'><pre>666</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                )</span></pre></td></tr><tr><td class='line-number'><a name='L667' href='#L667'><pre>667</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L668' href='#L668'><pre>668</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            None =&gt; <span class='region red'>failure_result(&quot;resource not found&quot;, 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L669' href='#L669'><pre>669</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L670' href='#L670'><pre>670</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L671' href='#L671'><pre>671</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L672' href='#L672'><pre>672</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;get_resource Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L673' href='#L673'><pre>673</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L674' href='#L674'><pre>674</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L675' href='#L675'><pre>675</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L676' href='#L676'><pre>676</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_entire_resource_list() -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L677' href='#L677'><pre>677</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L678' href='#L678'><pre>678</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L679' href='#L679'><pre>679</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_entire_resource_list(None)</span></pre></td></tr><tr><td class='line-number'><a name='L680' href='#L680'><pre>680</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L681' href='#L681'><pre>681</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resources) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L682' href='#L682'><pre>682</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L683' href='#L683'><pre>683</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resource_list = resources</span></pre></td></tr><tr><td class='line-number'><a name='L684' href='#L684'><pre>684</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .iter()</span></pre></td></tr><tr><td class='line-number'><a name='L685' href='#L685'><pre>685</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .map(</span>|it| <span class='region red'>create_fbs_resource_info(&amp;mut builder, it)</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L686' href='#L686'><pre>686</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                .collect::&lt;Vec&lt;_&gt;&gt;();</span></pre></td></tr><tr><td class='line-number'><a name='L687' href='#L687'><pre>687</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures = builder.create_vector(&amp;fbs_resource_list);</span></pre></td></tr><tr><td class='line-number'><a name='L688' href='#L688'><pre>688</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoures_args = FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L689' href='#L689'><pre>689</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                resources: Some(fbs_resoures),</span></pre></td></tr><tr><td class='line-number'><a name='L690' href='#L690'><pre>690</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            };</span></pre></td></tr><tr><td class='line-number'><a name='L691' href='#L691'><pre>691</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let fbs_resoure_list = FBSResourceInfoList::create(&amp;mut builder, &amp;fbs_resoures_args);</span></pre></td></tr><tr><td class='line-number'><a name='L692' href='#L692'><pre>692</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L693' href='#L693'><pre>693</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L694' href='#L694'><pre>694</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                fbs_resoure_list.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L695' href='#L695'><pre>695</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                ResourceContainer::FBSResourceInfoList,</span></pre></td></tr><tr><td class='line-number'><a name='L696' href='#L696'><pre>696</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span></pre></td></tr><tr><td class='line-number'><a name='L697' href='#L697'><pre>697</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L698' href='#L698'><pre>698</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L699' href='#L699'><pre>699</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L700' href='#L700'><pre>700</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;get_entire_resource_list Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L701' href='#L701'><pre>701</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L702' href='#L702'><pre>702</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L703' href='#L703'><pre>703</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L704' href='#L704'><pre>704</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_resource_install_path(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L705' href='#L705'><pre>705</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;res_type&quot;);</pre></td></tr><tr><td class='line-number'><a name='L706' href='#L706'><pre>706</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L707' href='#L707'><pre>707</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L708' href='#L708'><pre>708</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L709' href='#L709'><pre>709</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L710' href='#L710'><pre>710</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        .get_resource_install_path(res_type)</span></pre></td></tr><tr><td class='line-number'><a name='L711' href='#L711'><pre>711</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L712' href='#L712'><pre>712</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>path) =&gt; string_result(path)</span>,</pre></td></tr><tr><td class='line-number'><a name='L713' href='#L713'><pre>713</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L714' href='#L714'><pre>714</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L715' href='#L715'><pre>715</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            failure_result(&amp;format!(&quot;get_resource_install_path Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L716' href='#L716'><pre>716</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L717' href='#L717'><pre>717</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L718' href='#L718'><pre>718</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L719' href='#L719'><pre>719</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn update_resource_to_database(params: HashMap&lt;String, String&gt;) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L720' href='#L720'><pre>720</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    require_params!</span>(<span class='region red'>params</span>, &quot;resource_info_json&quot;);</pre></td></tr><tr><td class='line-number'><a name='L721' href='#L721'><pre>721</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>resource_info_json = params[&quot;resource_info_json&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L722' href='#L722'><pre>722</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match serde_json::from_str::&lt;ResourceInfo&gt;(&amp;resource_info_json)</span> {</pre></td></tr><tr><td class='line-number'><a name='L723' href='#L723'><pre>723</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Ok(<span class='region red'>resource_info) =&gt; match ResourceManager::get_instance()</span></pre></td></tr><tr><td class='line-number'><a name='L724' href='#L724'><pre>724</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .get_resource()</span></pre></td></tr><tr><td class='line-number'><a name='L725' href='#L725'><pre>725</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .update_resource_to_database(&amp;resource_info)</span></pre></td></tr><tr><td class='line-number'><a name='L726' href='#L726'><pre>726</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        {</pre></td></tr><tr><td class='line-number'><a name='L727' href='#L727'><pre>727</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Ok(_) =&gt; <span class='region red'>success_result()</span>,</pre></td></tr><tr><td class='line-number'><a name='L728' href='#L728'><pre>728</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            Err(<span class='region red'>e) =&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L729' href='#L729'><pre>729</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                let (code, msg) = get_error_code_and_message(&amp;e);</span></pre></td></tr><tr><td class='line-number'><a name='L730' href='#L730'><pre>730</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                failure_result(&amp;format!(&quot;update_resource_to_database Error:{}&quot;, msg), code)</span></pre></td></tr><tr><td class='line-number'><a name='L731' href='#L731'><pre>731</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L732' href='#L732'><pre>732</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L733' href='#L733'><pre>733</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Err(<span class='region red'>e) =&gt; failure_result(&amp;format!(&quot;serde_json Error:{}&quot;, e), 100)</span>,</pre></td></tr><tr><td class='line-number'><a name='L734' href='#L734'><pre>734</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L735' href='#L735'><pre>735</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L736' href='#L736'><pre>736</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_device_condition(params: HashMap&lt;String, String&gt;) -&gt; DeviceCondition {</span></pre></td></tr><tr><td class='line-number'><a name='L737' href='#L737'><pre>737</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type_value = params[&quot;res_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L738' href='#L738'><pre>738</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let model = params[&quot;model&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L739' href='#L739'><pre>739</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let pid = params[&quot;pid&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L740' href='#L740'><pre>740</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let type_id = params[&quot;type_id&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L741' href='#L741'><pre>741</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let prod_no = params[&quot;prod_no&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L742' href='#L742'><pre>742</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let device_type = params[&quot;device_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L743' href='#L743'><pre>743</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let device_net_type = params[&quot;device_net_type&quot;].to_string();</span></pre></td></tr><tr><td class='line-number'><a name='L744' href='#L744'><pre>744</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let res_type = ResourceType::from(res_type_value);</span></pre></td></tr><tr><td class='line-number'><a name='L745' href='#L745'><pre>745</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    DeviceCondition::new(</span></pre></td></tr><tr><td class='line-number'><a name='L746' href='#L746'><pre>746</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        res_type,</span></pre></td></tr><tr><td class='line-number'><a name='L747' href='#L747'><pre>747</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        model,</span></pre></td></tr><tr><td class='line-number'><a name='L748' href='#L748'><pre>748</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        type_id,</span></pre></td></tr><tr><td class='line-number'><a name='L749' href='#L749'><pre>749</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        prod_no,</span></pre></td></tr><tr><td class='line-number'><a name='L750' href='#L750'><pre>750</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        device_type,</span></pre></td></tr><tr><td class='line-number'><a name='L751' href='#L751'><pre>751</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        device_net_type,</span></pre></td></tr><tr><td class='line-number'><a name='L752' href='#L752'><pre>752</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        pid,</span></pre></td></tr><tr><td class='line-number'><a name='L753' href='#L753'><pre>753</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    )</span></pre></td></tr><tr><td class='line-number'><a name='L754' href='#L754'><pre>754</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L755' href='#L755'><pre>755</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn create_fbs_resource_info&lt;&apos;a&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L756' href='#L756'><pre>756</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder: &amp;mut FlatBufferBuilder&lt;&apos;a&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L757' href='#L757'><pre>757</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    resource_info: &amp;ResourceInfo,</span></pre></td></tr><tr><td class='line-number'><a name='L758' href='#L758'><pre>758</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; WIPOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L759' href='#L759'><pre>759</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let fbs_resource_info_args = FBSResourceInfoArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L760' href='#L760'><pre>760</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        id: resource_info.id,</span></pre></td></tr><tr><td class='line-number'><a name='L761' href='#L761'><pre>761</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        name: Some(builder.create_string(&amp;resource_info.name)),</span></pre></td></tr><tr><td class='line-number'><a name='L762' href='#L762'><pre>762</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        version: Some(builder.create_string(&amp;resource_info.version)),</span></pre></td></tr><tr><td class='line-number'><a name='L763' href='#L763'><pre>763</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        resource_type: convert_to_fbs_resource_type(&amp;resource_info.resource_type),</span></pre></td></tr><tr><td class='line-number'><a name='L764' href='#L764'><pre>764</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        create_time: resource_info.create_time,</span></pre></td></tr><tr><td class='line-number'><a name='L765' href='#L765'><pre>765</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        update_time: resource_info.update_time,</span></pre></td></tr><tr><td class='line-number'><a name='L766' href='#L766'><pre>766</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        download_url: Some(builder.create_string(&amp;resource_info.download_url)),</span></pre></td></tr><tr><td class='line-number'><a name='L767' href='#L767'><pre>767</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        path: resource_info</span></pre></td></tr><tr><td class='line-number'><a name='L768' href='#L768'><pre>768</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .path</span></pre></td></tr><tr><td class='line-number'><a name='L769' href='#L769'><pre>769</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .as_ref()</span></pre></td></tr><tr><td class='line-number'><a name='L770' href='#L770'><pre>770</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|it| <span class='region red'>builder.create_string(it)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L771' href='#L771'><pre>771</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        index_path: resource_info</span></pre></td></tr><tr><td class='line-number'><a name='L772' href='#L772'><pre>772</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .index_path</span></pre></td></tr><tr><td class='line-number'><a name='L773' href='#L773'><pre>773</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .as_ref()</span></pre></td></tr><tr><td class='line-number'><a name='L774' href='#L774'><pre>774</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|it| <span class='region red'>builder.create_string(it)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L775' href='#L775'><pre>775</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        is_preset: resource_info.is_preset,</span></pre></td></tr><tr><td class='line-number'><a name='L776' href='#L776'><pre>776</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        is_active: resource_info.is_active,</span></pre></td></tr><tr><td class='line-number'><a name='L777' href='#L777'><pre>777</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        is_force_upgrade: resource_info.is_force_upgrade,</span></pre></td></tr><tr><td class='line-number'><a name='L778' href='#L778'><pre>778</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        hide_status_bar: resource_info.hide_status_bar,</span></pre></td></tr><tr><td class='line-number'><a name='L779' href='#L779'><pre>779</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        rules_json: resource_info</span></pre></td></tr><tr><td class='line-number'><a name='L780' href='#L780'><pre>780</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .rules_json</span></pre></td></tr><tr><td class='line-number'><a name='L781' href='#L781'><pre>781</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .as_ref()</span></pre></td></tr><tr><td class='line-number'><a name='L782' href='#L782'><pre>782</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|it| <span class='region red'>builder.create_string(it)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L783' href='#L783'><pre>783</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        remote_page_url: resource_info</span></pre></td></tr><tr><td class='line-number'><a name='L784' href='#L784'><pre>784</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .remote_page_url</span></pre></td></tr><tr><td class='line-number'><a name='L785' href='#L785'><pre>785</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .as_ref()</span></pre></td></tr><tr><td class='line-number'><a name='L786' href='#L786'><pre>786</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .map(</span>|it| <span class='region red'>builder.create_string(it)</span><span class='region red'>),</span></pre></td></tr><tr><td class='line-number'><a name='L787' href='#L787'><pre>787</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        status: resource_info.status,</span></pre></td></tr><tr><td class='line-number'><a name='L788' href='#L788'><pre>788</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        is_server_latest: resource_info.is_server_latest,</span></pre></td></tr><tr><td class='line-number'><a name='L789' href='#L789'><pre>789</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        hash: Some(builder.create_string(&amp;resource_info.hash)),</span></pre></td></tr><tr><td class='line-number'><a name='L790' href='#L790'><pre>790</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        download_priority: convert_to_fbs_resource_download_priority(</span></pre></td></tr><tr><td class='line-number'><a name='L791' href='#L791'><pre>791</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            &amp;resource_info.download_priority,</span></pre></td></tr><tr><td class='line-number'><a name='L792' href='#L792'><pre>792</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ),</span></pre></td></tr><tr><td class='line-number'><a name='L793' href='#L793'><pre>793</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    };</span></pre></td></tr><tr><td class='line-number'><a name='L794' href='#L794'><pre>794</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfo::create(builder, &amp;fbs_resource_info_args)</span></pre></td></tr><tr><td class='line-number'><a name='L795' href='#L795'><pre>795</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L796' href='#L796'><pre>796</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L797' href='#L797'><pre>797</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn convert_to_fbs_resource_type(resource_type: &amp;ResourceType) -&gt; FBSResourceType {</span></pre></td></tr><tr><td class='line-number'><a name='L798' href='#L798'><pre>798</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match resource_type</span> {</pre></td></tr><tr><td class='line-number'><a name='L799' href='#L799'><pre>799</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::DeviceConfig =&gt; <span class='region red'>FBSResourceType::DeviceConfig</span>,</pre></td></tr><tr><td class='line-number'><a name='L800' href='#L800'><pre>800</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::MPaaS =&gt; <span class='region red'>FBSResourceType::MPaaS</span>,</pre></td></tr><tr><td class='line-number'><a name='L801' href='#L801'><pre>801</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::ConfigApp =&gt; <span class='region red'>FBSResourceType::ConfigApp</span>,</pre></td></tr><tr><td class='line-number'><a name='L802' href='#L802'><pre>802</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::AppFuncModel =&gt; <span class='region red'>FBSResourceType::AppFuncModel</span>,</pre></td></tr><tr><td class='line-number'><a name='L803' href='#L803'><pre>803</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::ConfigFile =&gt; <span class='region red'>FBSResourceType::ConfigFile</span>,</pre></td></tr><tr><td class='line-number'><a name='L804' href='#L804'><pre>804</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::H5 =&gt; <span class='region red'>FBSResourceType::H5</span>,</pre></td></tr><tr><td class='line-number'><a name='L805' href='#L805'><pre>805</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::APICloud =&gt; <span class='region red'>FBSResourceType::APICloud</span>,</pre></td></tr><tr><td class='line-number'><a name='L806' href='#L806'><pre>806</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::Shadow =&gt; <span class='region red'>FBSResourceType::Shadow</span>,</pre></td></tr><tr><td class='line-number'><a name='L807' href='#L807'><pre>807</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceType::All =&gt; <span class='region red'>FBSResourceType::All</span>,</pre></td></tr><tr><td class='line-number'><a name='L808' href='#L808'><pre>808</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>FBSResourceType::Other</span>,</pre></td></tr><tr><td class='line-number'><a name='L809' href='#L809'><pre>809</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L810' href='#L810'><pre>810</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L811' href='#L811'><pre>811</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn convert_to_fbs_resource_download_priority(</span></pre></td></tr><tr><td class='line-number'><a name='L812' href='#L812'><pre>812</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    priority: &amp;ResourceDownloadPriority,</span></pre></td></tr><tr><td class='line-number'><a name='L813' href='#L813'><pre>813</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; FBSResourceDownloadPriority {</span></pre></td></tr><tr><td class='line-number'><a name='L814' href='#L814'><pre>814</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match priority</span> {</pre></td></tr><tr><td class='line-number'><a name='L815' href='#L815'><pre>815</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceDownloadPriority::Normal =&gt; <span class='region red'>FBSResourceDownloadPriority::Normal</span>,</pre></td></tr><tr><td class='line-number'><a name='L816' href='#L816'><pre>816</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceDownloadPriority::High =&gt; <span class='region red'>FBSResourceDownloadPriority::High</span>,</pre></td></tr><tr><td class='line-number'><a name='L817' href='#L817'><pre>817</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L818' href='#L818'><pre>818</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L819' href='#L819'><pre>819</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn invalid_arg_result(error_message: &amp;str) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L820' href='#L820'><pre>820</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    failure_result(error_message, 900003)</span></pre></td></tr><tr><td class='line-number'><a name='L821' href='#L821'><pre>821</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L822' href='#L822'><pre>822</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn string_result(result: String) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L823' href='#L823'><pre>823</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L824' href='#L824'><pre>824</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let value = builder.create_string(&amp;result);</span></pre></td></tr><tr><td class='line-number'><a name='L825' href='#L825'><pre>825</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let string_result = StrWrapper::create(&amp;mut builder, &amp;StrWrapperArgs { value: Some(value) });</span></pre></td></tr><tr><td class='line-number'><a name='L826' href='#L826'><pre>826</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L827' href='#L827'><pre>827</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L828' href='#L828'><pre>828</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        string_result.as_union_value(),</span></pre></td></tr><tr><td class='line-number'><a name='L829' href='#L829'><pre>829</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        ResourceContainer::StrWrapper,</span></pre></td></tr><tr><td class='line-number'><a name='L830' href='#L830'><pre>830</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    )</span></pre></td></tr><tr><td class='line-number'><a name='L831' href='#L831'><pre>831</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L832' href='#L832'><pre>832</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L833' href='#L833'><pre>833</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn failure_result(error_message: &amp;str, error_code: i32) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L834' href='#L834'><pre>834</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    error!</span>(</pre></td></tr><tr><td class='line-number'><a name='L835' href='#L835'><pre>835</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>&quot;Operation Error : {} error_code : {}&quot;</span>,</pre></td></tr><tr><td class='line-number'><a name='L836' href='#L836'><pre>836</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        error_message, error_code</pre></td></tr><tr><td class='line-number'><a name='L837' href='#L837'><pre>837</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    );</pre></td></tr><tr><td class='line-number'><a name='L838' href='#L838'><pre>838</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L839' href='#L839'><pre>839</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let error_message = builder.create_string(error_message);</span></pre></td></tr><tr><td class='line-number'><a name='L840' href='#L840'><pre>840</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let wrapper = NoneWrapper::create(&amp;mut builder, &amp;NoneWrapperArgs {});</span></pre></td></tr><tr><td class='line-number'><a name='L841' href='#L841'><pre>841</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let userdomain_result = ResourceFlat::create(</span></pre></td></tr><tr><td class='line-number'><a name='L842' href='#L842'><pre>842</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L843' href='#L843'><pre>843</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;ResourceFlatArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L844' href='#L844'><pre>844</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container_type: ResourceContainer::NoneWrapper,</span></pre></td></tr><tr><td class='line-number'><a name='L845' href='#L845'><pre>845</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container: Some(wrapper.as_union_value()),</span></pre></td></tr><tr><td class='line-number'><a name='L846' href='#L846'><pre>846</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            code: error_code,</span></pre></td></tr><tr><td class='line-number'><a name='L847' href='#L847'><pre>847</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            error: Some(error_message),</span></pre></td></tr><tr><td class='line-number'><a name='L848' href='#L848'><pre>848</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        },</span></pre></td></tr><tr><td class='line-number'><a name='L849' href='#L849'><pre>849</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    );</span></pre></td></tr><tr><td class='line-number'><a name='L850' href='#L850'><pre>850</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish(userdomain_result, None);</span></pre></td></tr><tr><td class='line-number'><a name='L851' href='#L851'><pre>851</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finished_data().to_vec()</span></pre></td></tr><tr><td class='line-number'><a name='L852' href='#L852'><pre>852</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L853' href='#L853'><pre>853</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L854' href='#L854'><pre>854</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn data_result(</span></pre></td></tr><tr><td class='line-number'><a name='L855' href='#L855'><pre>855</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder: &amp;mut FlatBufferBuilder,</span></pre></td></tr><tr><td class='line-number'><a name='L856' href='#L856'><pre>856</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    data: WIPOffset&lt;UnionWIPOffset&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L857' href='#L857'><pre>857</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    container_type: ResourceContainer,</span></pre></td></tr><tr><td class='line-number'><a name='L858' href='#L858'><pre>858</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L859' href='#L859'><pre>859</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let userdomain_result = ResourceFlat::create(</span></pre></td></tr><tr><td class='line-number'><a name='L860' href='#L860'><pre>860</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        builder,</span></pre></td></tr><tr><td class='line-number'><a name='L861' href='#L861'><pre>861</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;ResourceFlatArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L862' href='#L862'><pre>862</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container_type,</span></pre></td></tr><tr><td class='line-number'><a name='L863' href='#L863'><pre>863</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container: Some(data),</span></pre></td></tr><tr><td class='line-number'><a name='L864' href='#L864'><pre>864</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            code: SUCCESS_CODE,</span></pre></td></tr><tr><td class='line-number'><a name='L865' href='#L865'><pre>865</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            error: None,</span></pre></td></tr><tr><td class='line-number'><a name='L866' href='#L866'><pre>866</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        },</span></pre></td></tr><tr><td class='line-number'><a name='L867' href='#L867'><pre>867</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    );</span></pre></td></tr><tr><td class='line-number'><a name='L868' href='#L868'><pre>868</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish(userdomain_result, None);</span></pre></td></tr><tr><td class='line-number'><a name='L869' href='#L869'><pre>869</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finished_data().to_vec()</span></pre></td></tr><tr><td class='line-number'><a name='L870' href='#L870'><pre>870</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L871' href='#L871'><pre>871</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L872' href='#L872'><pre>872</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn success_result() -&gt; Vec&lt;u8&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L873' href='#L873'><pre>873</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    debug!</span>(<span class='region red'>&quot;Operation successful&quot;</span>);</pre></td></tr><tr><td class='line-number'><a name='L874' href='#L874'><pre>874</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);</span></pre></td></tr><tr><td class='line-number'><a name='L875' href='#L875'><pre>875</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let wrapper = NoneWrapper::create(&amp;mut builder, &amp;NoneWrapperArgs {});</span></pre></td></tr><tr><td class='line-number'><a name='L876' href='#L876'><pre>876</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let userdomain_result = ResourceFlat::create(</span></pre></td></tr><tr><td class='line-number'><a name='L877' href='#L877'><pre>877</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;mut builder,</span></pre></td></tr><tr><td class='line-number'><a name='L878' href='#L878'><pre>878</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;ResourceFlatArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L879' href='#L879'><pre>879</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container_type: ResourceContainer::NoneWrapper,</span></pre></td></tr><tr><td class='line-number'><a name='L880' href='#L880'><pre>880</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            container: Some(wrapper.as_union_value()),</span></pre></td></tr><tr><td class='line-number'><a name='L881' href='#L881'><pre>881</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            code: SUCCESS_CODE,</span></pre></td></tr><tr><td class='line-number'><a name='L882' href='#L882'><pre>882</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            error: None,</span></pre></td></tr><tr><td class='line-number'><a name='L883' href='#L883'><pre>883</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        },</span></pre></td></tr><tr><td class='line-number'><a name='L884' href='#L884'><pre>884</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    );</span></pre></td></tr><tr><td class='line-number'><a name='L885' href='#L885'><pre>885</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish(userdomain_result, None);</span></pre></td></tr><tr><td class='line-number'><a name='L886' href='#L886'><pre>886</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finished_data().to_vec()</span></pre></td></tr><tr><td class='line-number'><a name='L887' href='#L887'><pre>887</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L888' href='#L888'><pre>888</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>fn get_error_code_and_message(err: &amp;ResourceError) -&gt; (i32, String) {</span></pre></td></tr><tr><td class='line-number'><a name='L889' href='#L889'><pre>889</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match err</span> {</pre></td></tr><tr><td class='line-number'><a name='L890' href='#L890'><pre>890</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        ResourceError::EmptyResultError(<span class='region red'>e) =&gt; (100001, format!(&quot;request failed:{}&quot;, e))</span>,</pre></td></tr><tr><td class='line-number'><a name='L891' href='#L891'><pre>891</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>(100, format!(&quot;{}&quot;, err))</span>,</pre></td></tr><tr><td class='line-number'><a name='L892' href='#L892'><pre>892</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L893' href='#L893'><pre>893</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr></table></div></body></html>