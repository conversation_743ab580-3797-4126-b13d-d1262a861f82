<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../style.css'><script src='../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/logic_engine_rust/rust_logicEngine/src/device_config/attr.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device_config::deserialize_rules::Validator;</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device_config::value_range::ValueRange;</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::de::{MapAccess, Visitor};</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::{Deserialize, Deserializer, Serialize};</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::fmt;</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize)]</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct Attribute {</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub name: String,</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub code: Option&lt;Vec&lt;String&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub desc: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub default_value: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub readable: Option&lt;bool&gt;,</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub writable: Option&lt;bool&gt;,</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub invisible: Option&lt;bool&gt;,</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub value_range: ValueRange,</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub operation_type: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Attribute {</pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    fn is_valid(&amp;self) -&gt; bool {</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let writable = self.writable.unwrap_or(false);</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let operation_type_is_valid = <div class='tooltip'><span class='region red'>matches!</span><span class='tooltip-content'>0</span></div>(<div class='tooltip'><span class='region red'>&amp;self.operation_type, Some(operation_type) if operation_type == &quot;I&quot;</span><span class='tooltip-content'>0</span></div> || <div class='tooltip'><span class='region red'>operation_type == &quot;G&quot;</span><span class='tooltip-content'>0</span></div> || <div class='tooltip'><span class='region red'>operation_type</span><span class='tooltip-content'>0</span></div> == &quot;IG<div class='tooltip'><span class='region red'>&quot;</span><span class='tooltip-content'>0</span></div>);</pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        if writable {</pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>!self.name.is_empty()</span> &amp;&amp; <span class='region red'>self.value_range.is_valid()</span> &amp;&amp; <span class='region red'>operation_type_is_valid</span></pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        } else {</pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            !self.name.is_empty() &amp;&amp; self.value_range.is_valid()</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre>//Alarm的自定义反序列化器</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for Attribute {</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    where</pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        D: Deserializer&lt;&apos;de&gt;,</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        deserializer.deserialize_map(AttributeVisitor)</pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='skipped-line'></td><td class='code'><pre>struct AttributeVisitor;</pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for AttributeVisitor {</pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Value = Attribute;</pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        formatter.write_str(&quot;a struct of Attribute&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    where</pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        A: MapAccess&lt;&apos;de&gt;,</pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut name = String::new();</pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut desc = None;</pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut code = None;</pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut default_value: Option&lt;String&gt; = None;</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut readable = None;</pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut writable = None;</pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut invisible = None;</pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut value_range = ValueRange::Unknown;</pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let mut operation_type = None;</pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='covered-line'><pre>144</pre></td><td class='code'><pre>        while let Some(<div class='tooltip'>key<span class='tooltip-content'>96</span></div>) = map.next_key::&lt;String&gt;()<div class='tooltip'><span class='region red'>?</span><span class='tooltip-content'>0</span></div> {</pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='covered-line'><pre>96</pre></td><td class='code'><pre>            match key.as_str() {</pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='covered-line'><pre>96</pre></td><td class='code'><pre>                &quot;name&quot; =&gt; <div class='tooltip'>name = map.next_value().unwrap_or_else(<span class='tooltip-content'>48</span></div>|_| <div class='tooltip'><span class='region red'>String::new()</span><span class='tooltip-content'>0</span></div><div class='tooltip'>)<span class='tooltip-content'>48</span></div>,</pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;desc&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    desc = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;code&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    code = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;defaultValue&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    default_value = map.next_value().unwrap_or(None).and_then(</span>|value: String| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        if value.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        } else {</pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                            <span class='region red'>Some(value)</span></pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                        }</pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>}</span><span class='region red'>);</span></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;readable&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    readable = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;writable&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    writable = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;invisible&quot; =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    invisible = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                &quot;valueRange&quot; =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                    value_range = map.next_value().unwrap_or(ValueRange::Unknown);</pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                    if let ValueRange::List { data_list } = &amp;mut value_range {</pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                        if !data_list.is_empty() {</pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                            data_list.retain(|data| data.is_valid());</pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>                        }<div class='tooltip'><span class='red'></span><span class='tooltip-content'>0</span></div></pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    }<span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>&quot;operationType&quot;</span> =&gt; <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    operation_type = map.next_value().unwrap_or(None);</span></pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                }</span></pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    let _ = <span class='region red'>map.next_value::&lt;serde::de::IgnoredAny&gt;()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        Ok(Attribute {</pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            name,</pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            desc,</pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            code,</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            default_value,</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            readable,</pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            writable,</pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            invisible,</pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            value_range,</pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            operation_type,</pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        })</pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr></table></div></body></html>