<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../style.css'><script src='../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/logic_engine_rust/rust_logicEngine/src/device_config/deserialize_rules.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::de::Visitor;</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::{de, Deserialize, Deserializer};</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde_json::Value;</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::fmt;</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub trait Validator {</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    fn is_valid(&amp;self) -&gt; bool;</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn non_empty_string&lt;&apos;de, D&gt;(deserializer: D) -&gt; Result&lt;String, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    struct StringNotEmpty;</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for StringNotEmpty {</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        type Value = String;</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            formatter.write_str(&quot;a non-empty string&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_str&lt;E&gt;(self, v: &amp;str) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            if v.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Err(E::custom(&quot;string cannot be empty&quot;))</span></pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            } else {</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>Ok(v.to_owned())</span></pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>deserializer.deserialize_string(StringNotEmpty)</span></pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn deserialize_string_or_int_to_string&lt;&apos;de, D&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    deserializer: D,</span></pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Result&lt;Option&lt;String&gt;, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>value: Value</span> = <span class='region red'>Deserialize::deserialize(deserializer)</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    match <span class='region red'>value</span> {</pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Value::String(<span class='region red'>s) =&gt; Ok(Some(s))</span>,</pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Value::Number(<span class='region red'>n) =&gt; Ok(Some(n.to_string()))</span>,</pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>Ok(None)</span>,</pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn deserialize_any_to_string&lt;&apos;de, D&gt;(deserializer: D) -&gt; Result&lt;String, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    let <span class='region red'>value: Value</span> = <span class='region red'>Deserialize::deserialize(deserializer)</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    match <span class='region red'>value</span> {</pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Value::String(<span class='region red'>s) =&gt; Ok(s)</span>,</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        Value::Number(<span class='region red'>n) =&gt; Ok(n.to_string())</span>,</pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        _ =&gt; <span class='region red'>Ok(&quot;&quot;.to_string())</span>,</pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn deserialize_string_or_int&lt;&apos;de, D&gt;(deserializer: D) -&gt; Result&lt;i32, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    struct StringOrInt;</pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for StringOrInt {</pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        type Value = i32;</pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            formatter.write_str(&quot;an integer or a string containing an integer&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_i64&lt;E&gt;(self, value: i64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value as i32)</span></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_u64&lt;E&gt;(self, value: u64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value as i32)</span></pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_f64&lt;E&gt;(self, value: f64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(9999999)</span></pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_str&lt;E&gt;(self, value: &amp;str) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            value.parse::&lt;i32&gt;().or(Ok(9999999))</span></pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>deserializer.deserialize_any(StringOrInt)</span></pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn deserialize_string_int_or_float&lt;&apos;de, D&gt;(deserializer: D) -&gt; Result&lt;f32, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    struct StringOrFloat;</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for StringOrFloat {</pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        type Value = f32;</pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            formatter.write_str(&quot;a float or a string containing a float&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_i64&lt;E&gt;(self, value: i64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value as f32)</span></pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_u64&lt;E&gt;(self, value: u64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value as f32)</span></pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_f64&lt;E&gt;(self, value: f64) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value as f32)</span></pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_str&lt;E&gt;(self, value: &amp;str) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            value.parse::&lt;f32&gt;().or(Ok(9999999.0))</span></pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>deserializer.deserialize_any(StringOrFloat)</span></pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn deserialize_string_to_bool&lt;&apos;de, D&gt;(deserializer: D) -&gt; Result&lt;bool, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>where</span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>{</pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    struct StringToBool;</pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for StringToBool {</pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        type Value = bool;</pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            formatter.write_str(&quot;a string that can be interpreted as a boolean&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_str&lt;E&gt;(self, value: &amp;str) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value.eq_ignore_ascii_case(&quot;true&quot;))</span></pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>fn visit_bool&lt;E&gt;(self, value: bool) -&gt; Result&lt;Self::Value, E&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        where</span></pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            E: de::Error,</span></pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        {</span></pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            Ok(value)</span></pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        }</span></pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>deserializer.deserialize_any(StringToBool)</span></pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr></table></div></body></html>