<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../../style.css'><script src='../../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/resource_rust/rust_resource/src/features/flat/resource_generated.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>// automatically generated by the FlatBuffers compiler, do not modify</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>// @generated</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod com {</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod haier {</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod uhome {</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod uplus {</pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod rust {</pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod resource {</pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(unused_imports, dead_code)]</pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub mod fbs {</pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::mem;</pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use core::cmp::Ordering;</pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  extern crate flatbuffers;</pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  use self::flatbuffers::{EndianScalar, Follow};</pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MIN_FBSRESOURCE_TYPE: i8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MAX_FBSRESOURCE_TYPE: i8 = 9;</pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_camel_case_types)]</pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_VALUES_FBSRESOURCE_TYPE: [FBSResourceType; 10] = [</pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::DeviceConfig,</pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::MPaaS,</pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::ConfigApp,</pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::AppFuncModel,</pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::ConfigFile,</pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::Other,</pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::H5,</pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::APICloud,</pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::Shadow,</pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceType::All,</pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='skipped-line'></td><td class='code'><pre>];</pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]</pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[repr(transparent)]</pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceType(pub i8);</pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_upper_case_globals)]</pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const DeviceConfig: Self = Self(0);</pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const MPaaS: Self = Self(1);</pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ConfigApp: Self = Self(2);</pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const AppFuncModel: Self = Self(3);</pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ConfigFile: Self = Self(4);</pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const Other: Self = Self(5);</pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const H5: Self = Self(6);</pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const APICloud: Self = Self(7);</pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const Shadow: Self = Self(8);</pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const All: Self = Self(9);</pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MIN: i8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MAX: i8 = 9;</pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_VALUES: &amp;&apos;static [Self] = &amp;[</pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::DeviceConfig,</pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::MPaaS,</pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::ConfigApp,</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::AppFuncModel,</pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::ConfigFile,</pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::Other,</pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::H5,</pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::APICloud,</pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::Shadow,</pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::All,</pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ];</pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  /// Returns the variant&apos;s name or &quot;&quot; if unknown.</pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn variant_name(self) -&gt; Option&lt;&amp;&apos;static str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match self</span> {</pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::DeviceConfig =&gt; <span class='region red'>Some(&quot;DeviceConfig&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::MPaaS =&gt; <span class='region red'>Some(&quot;MPaaS&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::ConfigApp =&gt; <span class='region red'>Some(&quot;ConfigApp&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::AppFuncModel =&gt; <span class='region red'>Some(&quot;AppFuncModel&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::ConfigFile =&gt; <span class='region red'>Some(&quot;ConfigFile&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::Other =&gt; <span class='region red'>Some(&quot;Other&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::H5 =&gt; <span class='region red'>Some(&quot;H5&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::APICloud =&gt; <span class='region red'>Some(&quot;APICloud&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::Shadow =&gt; <span class='region red'>Some(&quot;Shadow&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::All =&gt; <span class='region red'>Some(&quot;All&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      _ =&gt; <span class='region red'>None</span>,</pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter) -&gt; core::fmt::Result </span>{</pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>name</span>) = <span class='region red'>self.variant_name()</span> {</pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_str(name)</span></pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_fmt(format_args!(&quot;&lt;UNKNOWN {:?}&gt;&quot;, self.0))</span></pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = Self;</pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = flatbuffers::read_scalar_at::&lt;i8&gt;(buf, loc);</span></pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Push for FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Output = FBSResourceType;</pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[inline]</pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>unsafe fn push(&amp;self, dst: &amp;mut [u8], _written_len: usize) {</span></pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        flatbuffers::emplace_scalar::&lt;i8&gt;(dst, self.0);</span></pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::EndianScalar for FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Scalar = i8;</pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn to_little_endian(self) -&gt; i8 {</span></pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.0.to_le()</span></pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(clippy::wrong_self_convention)]</pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn from_little_endian(v: i8) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = i8::from_le(v);</span></pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Verifiable for FBSResourceType {</pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L179' href='#L179'><pre>179</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L180' href='#L180'><pre>180</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L181' href='#L181'><pre>181</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>i8::run_verifier(v, pos)</span></pre></td></tr><tr><td class='line-number'><a name='L182' href='#L182'><pre>182</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L183' href='#L183'><pre>183</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L184' href='#L184'><pre>184</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L185' href='#L185'><pre>185</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::SimpleToVerifyInSlice for FBSResourceType {}</pre></td></tr><tr><td class='line-number'><a name='L186' href='#L186'><pre>186</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L187' href='#L187'><pre>187</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MIN_FBSRESOURCE_DOWNLOAD_PRIORITY: i8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L188' href='#L188'><pre>188</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L189' href='#L189'><pre>189</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MAX_FBSRESOURCE_DOWNLOAD_PRIORITY: i8 = 1;</pre></td></tr><tr><td class='line-number'><a name='L190' href='#L190'><pre>190</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L191' href='#L191'><pre>191</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_camel_case_types)]</pre></td></tr><tr><td class='line-number'><a name='L192' href='#L192'><pre>192</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_VALUES_FBSRESOURCE_DOWNLOAD_PRIORITY: [FBSResourceDownloadPriority; 2] = [</pre></td></tr><tr><td class='line-number'><a name='L193' href='#L193'><pre>193</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceDownloadPriority::Normal,</pre></td></tr><tr><td class='line-number'><a name='L194' href='#L194'><pre>194</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  FBSResourceDownloadPriority::High,</pre></td></tr><tr><td class='line-number'><a name='L195' href='#L195'><pre>195</pre></a></td><td class='skipped-line'></td><td class='code'><pre>];</pre></td></tr><tr><td class='line-number'><a name='L196' href='#L196'><pre>196</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L197' href='#L197'><pre>197</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]</pre></td></tr><tr><td class='line-number'><a name='L198' href='#L198'><pre>198</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[repr(transparent)]</pre></td></tr><tr><td class='line-number'><a name='L199' href='#L199'><pre>199</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceDownloadPriority(pub i8);</pre></td></tr><tr><td class='line-number'><a name='L200' href='#L200'><pre>200</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_upper_case_globals)]</pre></td></tr><tr><td class='line-number'><a name='L201' href='#L201'><pre>201</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L202' href='#L202'><pre>202</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const Normal: Self = Self(0);</pre></td></tr><tr><td class='line-number'><a name='L203' href='#L203'><pre>203</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const High: Self = Self(1);</pre></td></tr><tr><td class='line-number'><a name='L204' href='#L204'><pre>204</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L205' href='#L205'><pre>205</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MIN: i8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L206' href='#L206'><pre>206</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MAX: i8 = 1;</pre></td></tr><tr><td class='line-number'><a name='L207' href='#L207'><pre>207</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_VALUES: &amp;&apos;static [Self] = &amp;[</pre></td></tr><tr><td class='line-number'><a name='L208' href='#L208'><pre>208</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::Normal,</pre></td></tr><tr><td class='line-number'><a name='L209' href='#L209'><pre>209</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::High,</pre></td></tr><tr><td class='line-number'><a name='L210' href='#L210'><pre>210</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ];</pre></td></tr><tr><td class='line-number'><a name='L211' href='#L211'><pre>211</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  /// Returns the variant&apos;s name or &quot;&quot; if unknown.</pre></td></tr><tr><td class='line-number'><a name='L212' href='#L212'><pre>212</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn variant_name(self) -&gt; Option&lt;&amp;&apos;static str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L213' href='#L213'><pre>213</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match self</span> {</pre></td></tr><tr><td class='line-number'><a name='L214' href='#L214'><pre>214</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::Normal =&gt; <span class='region red'>Some(&quot;Normal&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L215' href='#L215'><pre>215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::High =&gt; <span class='region red'>Some(&quot;High&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L216' href='#L216'><pre>216</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      _ =&gt; <span class='region red'>None</span>,</pre></td></tr><tr><td class='line-number'><a name='L217' href='#L217'><pre>217</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L218' href='#L218'><pre>218</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L219' href='#L219'><pre>219</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L220' href='#L220'><pre>220</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L221' href='#L221'><pre>221</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter) -&gt; core::fmt::Result </span>{</pre></td></tr><tr><td class='line-number'><a name='L222' href='#L222'><pre>222</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>name</span>) = <span class='region red'>self.variant_name()</span> {</pre></td></tr><tr><td class='line-number'><a name='L223' href='#L223'><pre>223</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_str(name)</span></pre></td></tr><tr><td class='line-number'><a name='L224' href='#L224'><pre>224</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L225' href='#L225'><pre>225</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_fmt(format_args!(&quot;&lt;UNKNOWN {:?}&gt;&quot;, self.0))</span></pre></td></tr><tr><td class='line-number'><a name='L226' href='#L226'><pre>226</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L227' href='#L227'><pre>227</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L228' href='#L228'><pre>228</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L229' href='#L229'><pre>229</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L230' href='#L230'><pre>230</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = Self;</pre></td></tr><tr><td class='line-number'><a name='L231' href='#L231'><pre>231</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L232' href='#L232'><pre>232</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L233' href='#L233'><pre>233</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = flatbuffers::read_scalar_at::&lt;i8&gt;(buf, loc);</span></pre></td></tr><tr><td class='line-number'><a name='L234' href='#L234'><pre>234</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L235' href='#L235'><pre>235</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L236' href='#L236'><pre>236</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L237' href='#L237'><pre>237</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L238' href='#L238'><pre>238</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Push for FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L239' href='#L239'><pre>239</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Output = FBSResourceDownloadPriority;</pre></td></tr><tr><td class='line-number'><a name='L240' href='#L240'><pre>240</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[inline]</pre></td></tr><tr><td class='line-number'><a name='L241' href='#L241'><pre>241</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>unsafe fn push(&amp;self, dst: &amp;mut [u8], _written_len: usize) {</span></pre></td></tr><tr><td class='line-number'><a name='L242' href='#L242'><pre>242</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        flatbuffers::emplace_scalar::&lt;i8&gt;(dst, self.0);</span></pre></td></tr><tr><td class='line-number'><a name='L243' href='#L243'><pre>243</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L244' href='#L244'><pre>244</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L245' href='#L245'><pre>245</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L246' href='#L246'><pre>246</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::EndianScalar for FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L247' href='#L247'><pre>247</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Scalar = i8;</pre></td></tr><tr><td class='line-number'><a name='L248' href='#L248'><pre>248</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L249' href='#L249'><pre>249</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn to_little_endian(self) -&gt; i8 {</span></pre></td></tr><tr><td class='line-number'><a name='L250' href='#L250'><pre>250</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.0.to_le()</span></pre></td></tr><tr><td class='line-number'><a name='L251' href='#L251'><pre>251</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L252' href='#L252'><pre>252</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L253' href='#L253'><pre>253</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(clippy::wrong_self_convention)]</pre></td></tr><tr><td class='line-number'><a name='L254' href='#L254'><pre>254</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn from_little_endian(v: i8) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L255' href='#L255'><pre>255</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = i8::from_le(v);</span></pre></td></tr><tr><td class='line-number'><a name='L256' href='#L256'><pre>256</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L257' href='#L257'><pre>257</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L258' href='#L258'><pre>258</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L259' href='#L259'><pre>259</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L260' href='#L260'><pre>260</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Verifiable for FBSResourceDownloadPriority {</pre></td></tr><tr><td class='line-number'><a name='L261' href='#L261'><pre>261</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L262' href='#L262'><pre>262</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L263' href='#L263'><pre>263</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L264' href='#L264'><pre>264</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L265' href='#L265'><pre>265</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L266' href='#L266'><pre>266</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>i8::run_verifier(v, pos)</span></pre></td></tr><tr><td class='line-number'><a name='L267' href='#L267'><pre>267</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L268' href='#L268'><pre>268</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L269' href='#L269'><pre>269</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L270' href='#L270'><pre>270</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::SimpleToVerifyInSlice for FBSResourceDownloadPriority {}</pre></td></tr><tr><td class='line-number'><a name='L271' href='#L271'><pre>271</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L272' href='#L272'><pre>272</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MIN_RESOURCE_CONTAINER: u8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L273' href='#L273'><pre>273</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L274' href='#L274'><pre>274</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_MAX_RESOURCE_CONTAINER: u8 = 8;</pre></td></tr><tr><td class='line-number'><a name='L275' href='#L275'><pre>275</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[deprecated(since = &quot;2.0.0&quot;, note = &quot;Use associated constants instead. This will no longer be generated in 2021.&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L276' href='#L276'><pre>276</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_camel_case_types)]</pre></td></tr><tr><td class='line-number'><a name='L277' href='#L277'><pre>277</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub const ENUM_VALUES_RESOURCE_CONTAINER: [ResourceContainer; 9] = [</pre></td></tr><tr><td class='line-number'><a name='L278' href='#L278'><pre>278</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::NONE,</pre></td></tr><tr><td class='line-number'><a name='L279' href='#L279'><pre>279</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::NoneWrapper,</pre></td></tr><tr><td class='line-number'><a name='L280' href='#L280'><pre>280</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::StrWrapper,</pre></td></tr><tr><td class='line-number'><a name='L281' href='#L281'><pre>281</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::BoolWrapper,</pre></td></tr><tr><td class='line-number'><a name='L282' href='#L282'><pre>282</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::FBSResourceInfo,</pre></td></tr><tr><td class='line-number'><a name='L283' href='#L283'><pre>283</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::FBSResourceInfoList,</pre></td></tr><tr><td class='line-number'><a name='L284' href='#L284'><pre>284</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::FBSResourceInfoResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L285' href='#L285'><pre>285</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::FBSResourceInfoListResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L286' href='#L286'><pre>286</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ResourceContainer::FBSResourceInfoOnProgressResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L287' href='#L287'><pre>287</pre></a></td><td class='skipped-line'></td><td class='code'><pre>];</pre></td></tr><tr><td class='line-number'><a name='L288' href='#L288'><pre>288</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L289' href='#L289'><pre>289</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]</pre></td></tr><tr><td class='line-number'><a name='L290' href='#L290'><pre>290</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[repr(transparent)]</pre></td></tr><tr><td class='line-number'><a name='L291' href='#L291'><pre>291</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceContainer(pub u8);</pre></td></tr><tr><td class='line-number'><a name='L292' href='#L292'><pre>292</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[allow(non_upper_case_globals)]</pre></td></tr><tr><td class='line-number'><a name='L293' href='#L293'><pre>293</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L294' href='#L294'><pre>294</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const NONE: Self = Self(0);</pre></td></tr><tr><td class='line-number'><a name='L295' href='#L295'><pre>295</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const NoneWrapper: Self = Self(1);</pre></td></tr><tr><td class='line-number'><a name='L296' href='#L296'><pre>296</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const StrWrapper: Self = Self(2);</pre></td></tr><tr><td class='line-number'><a name='L297' href='#L297'><pre>297</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const BoolWrapper: Self = Self(3);</pre></td></tr><tr><td class='line-number'><a name='L298' href='#L298'><pre>298</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const FBSResourceInfo: Self = Self(4);</pre></td></tr><tr><td class='line-number'><a name='L299' href='#L299'><pre>299</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const FBSResourceInfoList: Self = Self(5);</pre></td></tr><tr><td class='line-number'><a name='L300' href='#L300'><pre>300</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const FBSResourceInfoResultCallBack: Self = Self(6);</pre></td></tr><tr><td class='line-number'><a name='L301' href='#L301'><pre>301</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const FBSResourceInfoListResultCallBack: Self = Self(7);</pre></td></tr><tr><td class='line-number'><a name='L302' href='#L302'><pre>302</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const FBSResourceInfoOnProgressResultCallBack: Self = Self(8);</pre></td></tr><tr><td class='line-number'><a name='L303' href='#L303'><pre>303</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L304' href='#L304'><pre>304</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MIN: u8 = 0;</pre></td></tr><tr><td class='line-number'><a name='L305' href='#L305'><pre>305</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_MAX: u8 = 8;</pre></td></tr><tr><td class='line-number'><a name='L306' href='#L306'><pre>306</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const ENUM_VALUES: &amp;&apos;static [Self] = &amp;[</pre></td></tr><tr><td class='line-number'><a name='L307' href='#L307'><pre>307</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::NONE,</pre></td></tr><tr><td class='line-number'><a name='L308' href='#L308'><pre>308</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::NoneWrapper,</pre></td></tr><tr><td class='line-number'><a name='L309' href='#L309'><pre>309</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::StrWrapper,</pre></td></tr><tr><td class='line-number'><a name='L310' href='#L310'><pre>310</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::BoolWrapper,</pre></td></tr><tr><td class='line-number'><a name='L311' href='#L311'><pre>311</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::FBSResourceInfo,</pre></td></tr><tr><td class='line-number'><a name='L312' href='#L312'><pre>312</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::FBSResourceInfoList,</pre></td></tr><tr><td class='line-number'><a name='L313' href='#L313'><pre>313</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::FBSResourceInfoResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L314' href='#L314'><pre>314</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::FBSResourceInfoListResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L315' href='#L315'><pre>315</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    Self::FBSResourceInfoOnProgressResultCallBack,</pre></td></tr><tr><td class='line-number'><a name='L316' href='#L316'><pre>316</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  ];</pre></td></tr><tr><td class='line-number'><a name='L317' href='#L317'><pre>317</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  /// Returns the variant&apos;s name or &quot;&quot; if unknown.</pre></td></tr><tr><td class='line-number'><a name='L318' href='#L318'><pre>318</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn variant_name(self) -&gt; Option&lt;&amp;&apos;static str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L319' href='#L319'><pre>319</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    match self</span> {</pre></td></tr><tr><td class='line-number'><a name='L320' href='#L320'><pre>320</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::NONE =&gt; <span class='region red'>Some(&quot;NONE&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L321' href='#L321'><pre>321</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::NoneWrapper =&gt; <span class='region red'>Some(&quot;NoneWrapper&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L322' href='#L322'><pre>322</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::StrWrapper =&gt; <span class='region red'>Some(&quot;StrWrapper&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L323' href='#L323'><pre>323</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::BoolWrapper =&gt; <span class='region red'>Some(&quot;BoolWrapper&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L324' href='#L324'><pre>324</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::FBSResourceInfo =&gt; <span class='region red'>Some(&quot;FBSResourceInfo&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L325' href='#L325'><pre>325</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::FBSResourceInfoList =&gt; <span class='region red'>Some(&quot;FBSResourceInfoList&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L326' href='#L326'><pre>326</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::FBSResourceInfoResultCallBack =&gt; <span class='region red'>Some(&quot;FBSResourceInfoResultCallBack&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L327' href='#L327'><pre>327</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::FBSResourceInfoListResultCallBack =&gt; <span class='region red'>Some(&quot;FBSResourceInfoListResultCallBack&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L328' href='#L328'><pre>328</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      Self::FBSResourceInfoOnProgressResultCallBack =&gt; <span class='region red'>Some(&quot;FBSResourceInfoOnProgressResultCallBack&quot;)</span>,</pre></td></tr><tr><td class='line-number'><a name='L329' href='#L329'><pre>329</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      _ =&gt; <span class='region red'>None</span>,</pre></td></tr><tr><td class='line-number'><a name='L330' href='#L330'><pre>330</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L331' href='#L331'><pre>331</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L332' href='#L332'><pre>332</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L333' href='#L333'><pre>333</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L334' href='#L334'><pre>334</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter) -&gt; core::fmt::Result </span>{</pre></td></tr><tr><td class='line-number'><a name='L335' href='#L335'><pre>335</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>name</span>) = <span class='region red'>self.variant_name()</span> {</pre></td></tr><tr><td class='line-number'><a name='L336' href='#L336'><pre>336</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_str(name)</span></pre></td></tr><tr><td class='line-number'><a name='L337' href='#L337'><pre>337</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L338' href='#L338'><pre>338</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>f.write_fmt(format_args!(&quot;&lt;UNKNOWN {:?}&gt;&quot;, self.0))</span></pre></td></tr><tr><td class='line-number'><a name='L339' href='#L339'><pre>339</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L340' href='#L340'><pre>340</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L341' href='#L341'><pre>341</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L342' href='#L342'><pre>342</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L343' href='#L343'><pre>343</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = Self;</pre></td></tr><tr><td class='line-number'><a name='L344' href='#L344'><pre>344</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L345' href='#L345'><pre>345</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L346' href='#L346'><pre>346</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = flatbuffers::read_scalar_at::&lt;u8&gt;(buf, loc);</span></pre></td></tr><tr><td class='line-number'><a name='L347' href='#L347'><pre>347</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L348' href='#L348'><pre>348</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L349' href='#L349'><pre>349</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L350' href='#L350'><pre>350</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L351' href='#L351'><pre>351</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Push for ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L352' href='#L352'><pre>352</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Output = ResourceContainer;</pre></td></tr><tr><td class='line-number'><a name='L353' href='#L353'><pre>353</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    #[inline]</pre></td></tr><tr><td class='line-number'><a name='L354' href='#L354'><pre>354</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>unsafe fn push(&amp;self, dst: &amp;mut [u8], _written_len: usize) {</span></pre></td></tr><tr><td class='line-number'><a name='L355' href='#L355'><pre>355</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        flatbuffers::emplace_scalar::&lt;u8&gt;(dst, self.0);</span></pre></td></tr><tr><td class='line-number'><a name='L356' href='#L356'><pre>356</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L357' href='#L357'><pre>357</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L358' href='#L358'><pre>358</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L359' href='#L359'><pre>359</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::EndianScalar for ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L360' href='#L360'><pre>360</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Scalar = u8;</pre></td></tr><tr><td class='line-number'><a name='L361' href='#L361'><pre>361</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L362' href='#L362'><pre>362</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn to_little_endian(self) -&gt; u8 {</span></pre></td></tr><tr><td class='line-number'><a name='L363' href='#L363'><pre>363</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.0.to_le()</span></pre></td></tr><tr><td class='line-number'><a name='L364' href='#L364'><pre>364</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L365' href='#L365'><pre>365</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L366' href='#L366'><pre>366</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(clippy::wrong_self_convention)]</pre></td></tr><tr><td class='line-number'><a name='L367' href='#L367'><pre>367</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn from_little_endian(v: u8) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L368' href='#L368'><pre>368</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let b = u8::from_le(v);</span></pre></td></tr><tr><td class='line-number'><a name='L369' href='#L369'><pre>369</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self(b)</span></pre></td></tr><tr><td class='line-number'><a name='L370' href='#L370'><pre>370</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L371' href='#L371'><pre>371</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L372' href='#L372'><pre>372</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L373' href='#L373'><pre>373</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Verifiable for ResourceContainer {</pre></td></tr><tr><td class='line-number'><a name='L374' href='#L374'><pre>374</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L375' href='#L375'><pre>375</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L376' href='#L376'><pre>376</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L377' href='#L377'><pre>377</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L378' href='#L378'><pre>378</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L379' href='#L379'><pre>379</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>u8::run_verifier(v, pos)</span></pre></td></tr><tr><td class='line-number'><a name='L380' href='#L380'><pre>380</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L381' href='#L381'><pre>381</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L382' href='#L382'><pre>382</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L383' href='#L383'><pre>383</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::SimpleToVerifyInSlice for ResourceContainer {}</pre></td></tr><tr><td class='line-number'><a name='L384' href='#L384'><pre>384</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceContainerUnionTableOffset {}</pre></td></tr><tr><td class='line-number'><a name='L385' href='#L385'><pre>385</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L386' href='#L386'><pre>386</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum NoneWrapperOffset {}</pre></td></tr><tr><td class='line-number'><a name='L387' href='#L387'><pre>387</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L388' href='#L388'><pre>388</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L389' href='#L389'><pre>389</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct NoneWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L390' href='#L390'><pre>390</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L391' href='#L391'><pre>391</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L392' href='#L392'><pre>392</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L393' href='#L393'><pre>393</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for NoneWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L394' href='#L394'><pre>394</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = NoneWrapper&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L395' href='#L395'><pre>395</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L396' href='#L396'><pre>396</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L397' href='#L397'><pre>397</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L398' href='#L398'><pre>398</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L399' href='#L399'><pre>399</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L400' href='#L400'><pre>400</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L401' href='#L401'><pre>401</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; NoneWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L402' href='#L402'><pre>402</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L403' href='#L403'><pre>403</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L404' href='#L404'><pre>404</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L405' href='#L405'><pre>405</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    NoneWrapper { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L406' href='#L406'><pre>406</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L407' href='#L407'><pre>407</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L408' href='#L408'><pre>408</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L409' href='#L409'><pre>409</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L410' href='#L410'><pre>410</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _args: &amp;&apos;args NoneWrapperArgs</span></pre></td></tr><tr><td class='line-number'><a name='L411' href='#L411'><pre>411</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;NoneWrapper&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L412' href='#L412'><pre>412</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = NoneWrapperBuilder::new(_fbb);</span></pre></td></tr><tr><td class='line-number'><a name='L413' href='#L413'><pre>413</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L414' href='#L414'><pre>414</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L415' href='#L415'><pre>415</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L416' href='#L416'><pre>416</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L417' href='#L417'><pre>417</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L418' href='#L418'><pre>418</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for NoneWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L419' href='#L419'><pre>419</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L420' href='#L420'><pre>420</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L421' href='#L421'><pre>421</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L422' href='#L422'><pre>422</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L423' href='#L423'><pre>423</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L424' href='#L424'><pre>424</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L425' href='#L425'><pre>425</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L426' href='#L426'><pre>426</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L427' href='#L427'><pre>427</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L428' href='#L428'><pre>428</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L429' href='#L429'><pre>429</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct NoneWrapperArgs {</pre></td></tr><tr><td class='line-number'><a name='L430' href='#L430'><pre>430</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L431' href='#L431'><pre>431</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for NoneWrapperArgs {</pre></td></tr><tr><td class='line-number'><a name='L432' href='#L432'><pre>432</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L433' href='#L433'><pre>433</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L434' href='#L434'><pre>434</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    NoneWrapperArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L435' href='#L435'><pre>435</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L436' href='#L436'><pre>436</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L437' href='#L437'><pre>437</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L438' href='#L438'><pre>438</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L439' href='#L439'><pre>439</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct NoneWrapperBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L440' href='#L440'><pre>440</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L441' href='#L441'><pre>441</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L442' href='#L442'><pre>442</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L443' href='#L443'><pre>443</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; NoneWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L444' href='#L444'><pre>444</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L445' href='#L445'><pre>445</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; NoneWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L446' href='#L446'><pre>446</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L447' href='#L447'><pre>447</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    NoneWrapperBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L448' href='#L448'><pre>448</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L449' href='#L449'><pre>449</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L450' href='#L450'><pre>450</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L451' href='#L451'><pre>451</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L452' href='#L452'><pre>452</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L453' href='#L453'><pre>453</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;NoneWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L454' href='#L454'><pre>454</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L455' href='#L455'><pre>455</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L456' href='#L456'><pre>456</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L457' href='#L457'><pre>457</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L458' href='#L458'><pre>458</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L459' href='#L459'><pre>459</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for NoneWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L460' href='#L460'><pre>460</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L461' href='#L461'><pre>461</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;NoneWrapper&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L462' href='#L462'><pre>462</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L463' href='#L463'><pre>463</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L464' href='#L464'><pre>464</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L465' href='#L465'><pre>465</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum StrWrapperOffset {}</pre></td></tr><tr><td class='line-number'><a name='L466' href='#L466'><pre>466</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L467' href='#L467'><pre>467</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L468' href='#L468'><pre>468</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct StrWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L469' href='#L469'><pre>469</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L470' href='#L470'><pre>470</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L471' href='#L471'><pre>471</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L472' href='#L472'><pre>472</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for StrWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L473' href='#L473'><pre>473</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = StrWrapper&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L474' href='#L474'><pre>474</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L475' href='#L475'><pre>475</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L476' href='#L476'><pre>476</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L477' href='#L477'><pre>477</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L478' href='#L478'><pre>478</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L479' href='#L479'><pre>479</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L480' href='#L480'><pre>480</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; StrWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L481' href='#L481'><pre>481</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_VALUE: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L482' href='#L482'><pre>482</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L483' href='#L483'><pre>483</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L484' href='#L484'><pre>484</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L485' href='#L485'><pre>485</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    StrWrapper { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L486' href='#L486'><pre>486</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L487' href='#L487'><pre>487</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L488' href='#L488'><pre>488</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L489' href='#L489'><pre>489</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L490' href='#L490'><pre>490</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args StrWrapperArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L491' href='#L491'><pre>491</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;StrWrapper&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L492' href='#L492'><pre>492</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = StrWrapperBuilder::new(_fbb)</span>;</pre></td></tr><tr><td class='line-number'><a name='L493' href='#L493'><pre>493</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.value</span> <span class='region red'>{ builder.add_value(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L494' href='#L494'><pre>494</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L495' href='#L495'><pre>495</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L496' href='#L496'><pre>496</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L497' href='#L497'><pre>497</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L498' href='#L498'><pre>498</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L499' href='#L499'><pre>499</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn value(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L500' href='#L500'><pre>500</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L501' href='#L501'><pre>501</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L502' href='#L502'><pre>502</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L503' href='#L503'><pre>503</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(StrWrapper::VT_VALUE, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L504' href='#L504'><pre>504</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L505' href='#L505'><pre>505</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L506' href='#L506'><pre>506</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L507' href='#L507'><pre>507</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for StrWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L508' href='#L508'><pre>508</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L509' href='#L509'><pre>509</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L510' href='#L510'><pre>510</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L511' href='#L511'><pre>511</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L512' href='#L512'><pre>512</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L513' href='#L513'><pre>513</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L514' href='#L514'><pre>514</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;value&quot;, Self::VT_VALUE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L515' href='#L515'><pre>515</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L516' href='#L516'><pre>516</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L517' href='#L517'><pre>517</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L518' href='#L518'><pre>518</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L519' href='#L519'><pre>519</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct StrWrapperArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L520' href='#L520'><pre>520</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub value: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L521' href='#L521'><pre>521</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L522' href='#L522'><pre>522</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for StrWrapperArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L523' href='#L523'><pre>523</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L524' href='#L524'><pre>524</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L525' href='#L525'><pre>525</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    StrWrapperArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L526' href='#L526'><pre>526</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      value: None,</span></pre></td></tr><tr><td class='line-number'><a name='L527' href='#L527'><pre>527</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L528' href='#L528'><pre>528</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L529' href='#L529'><pre>529</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L530' href='#L530'><pre>530</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L531' href='#L531'><pre>531</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct StrWrapperBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L532' href='#L532'><pre>532</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L533' href='#L533'><pre>533</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L534' href='#L534'><pre>534</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L535' href='#L535'><pre>535</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; StrWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L536' href='#L536'><pre>536</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L537' href='#L537'><pre>537</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_value(&amp;mut self, value: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L538' href='#L538'><pre>538</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(StrWrapper::VT_VALUE, value);</span></pre></td></tr><tr><td class='line-number'><a name='L539' href='#L539'><pre>539</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L540' href='#L540'><pre>540</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L541' href='#L541'><pre>541</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; StrWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L542' href='#L542'><pre>542</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L543' href='#L543'><pre>543</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    StrWrapperBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L544' href='#L544'><pre>544</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L545' href='#L545'><pre>545</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L546' href='#L546'><pre>546</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L547' href='#L547'><pre>547</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L548' href='#L548'><pre>548</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L549' href='#L549'><pre>549</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;StrWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L550' href='#L550'><pre>550</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L551' href='#L551'><pre>551</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L552' href='#L552'><pre>552</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L553' href='#L553'><pre>553</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L554' href='#L554'><pre>554</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L555' href='#L555'><pre>555</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for StrWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L556' href='#L556'><pre>556</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L557' href='#L557'><pre>557</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;StrWrapper&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L558' href='#L558'><pre>558</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;value&quot;, &amp;self.value());</span></pre></td></tr><tr><td class='line-number'><a name='L559' href='#L559'><pre>559</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L560' href='#L560'><pre>560</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L561' href='#L561'><pre>561</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L562' href='#L562'><pre>562</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum BoolWrapperOffset {}</pre></td></tr><tr><td class='line-number'><a name='L563' href='#L563'><pre>563</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L564' href='#L564'><pre>564</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L565' href='#L565'><pre>565</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct BoolWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L566' href='#L566'><pre>566</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L567' href='#L567'><pre>567</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L568' href='#L568'><pre>568</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L569' href='#L569'><pre>569</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for BoolWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L570' href='#L570'><pre>570</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = BoolWrapper&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L571' href='#L571'><pre>571</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L572' href='#L572'><pre>572</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L573' href='#L573'><pre>573</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L574' href='#L574'><pre>574</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L575' href='#L575'><pre>575</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L576' href='#L576'><pre>576</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L577' href='#L577'><pre>577</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; BoolWrapper&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L578' href='#L578'><pre>578</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_VALUE: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L579' href='#L579'><pre>579</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L580' href='#L580'><pre>580</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L581' href='#L581'><pre>581</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L582' href='#L582'><pre>582</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    BoolWrapper { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L583' href='#L583'><pre>583</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L584' href='#L584'><pre>584</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L585' href='#L585'><pre>585</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L586' href='#L586'><pre>586</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L587' href='#L587'><pre>587</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args BoolWrapperArgs</span></pre></td></tr><tr><td class='line-number'><a name='L588' href='#L588'><pre>588</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;BoolWrapper&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L589' href='#L589'><pre>589</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = BoolWrapperBuilder::new(_fbb);</span></pre></td></tr><tr><td class='line-number'><a name='L590' href='#L590'><pre>590</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_value(args.value);</span></pre></td></tr><tr><td class='line-number'><a name='L591' href='#L591'><pre>591</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L592' href='#L592'><pre>592</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L593' href='#L593'><pre>593</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L594' href='#L594'><pre>594</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L595' href='#L595'><pre>595</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L596' href='#L596'><pre>596</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn value(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L597' href='#L597'><pre>597</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L598' href='#L598'><pre>598</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L599' href='#L599'><pre>599</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L600' href='#L600'><pre>600</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(BoolWrapper::VT_VALUE, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L601' href='#L601'><pre>601</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L602' href='#L602'><pre>602</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L603' href='#L603'><pre>603</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L604' href='#L604'><pre>604</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for BoolWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L605' href='#L605'><pre>605</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L606' href='#L606'><pre>606</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L607' href='#L607'><pre>607</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L608' href='#L608'><pre>608</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L609' href='#L609'><pre>609</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L610' href='#L610'><pre>610</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L611' href='#L611'><pre>611</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;value&quot;, Self::VT_VALUE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L612' href='#L612'><pre>612</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L613' href='#L613'><pre>613</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L614' href='#L614'><pre>614</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L615' href='#L615'><pre>615</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L616' href='#L616'><pre>616</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct BoolWrapperArgs {</pre></td></tr><tr><td class='line-number'><a name='L617' href='#L617'><pre>617</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub value: bool,</pre></td></tr><tr><td class='line-number'><a name='L618' href='#L618'><pre>618</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L619' href='#L619'><pre>619</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for BoolWrapperArgs {</pre></td></tr><tr><td class='line-number'><a name='L620' href='#L620'><pre>620</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L621' href='#L621'><pre>621</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L622' href='#L622'><pre>622</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    BoolWrapperArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L623' href='#L623'><pre>623</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      value: false,</span></pre></td></tr><tr><td class='line-number'><a name='L624' href='#L624'><pre>624</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L625' href='#L625'><pre>625</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L626' href='#L626'><pre>626</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L627' href='#L627'><pre>627</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L628' href='#L628'><pre>628</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct BoolWrapperBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L629' href='#L629'><pre>629</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L630' href='#L630'><pre>630</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L631' href='#L631'><pre>631</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L632' href='#L632'><pre>632</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; BoolWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L633' href='#L633'><pre>633</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L634' href='#L634'><pre>634</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_value(&amp;mut self, value: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L635' href='#L635'><pre>635</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(BoolWrapper::VT_VALUE, value, false);</span></pre></td></tr><tr><td class='line-number'><a name='L636' href='#L636'><pre>636</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L637' href='#L637'><pre>637</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L638' href='#L638'><pre>638</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; BoolWrapperBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L639' href='#L639'><pre>639</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L640' href='#L640'><pre>640</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    BoolWrapperBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L641' href='#L641'><pre>641</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L642' href='#L642'><pre>642</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L643' href='#L643'><pre>643</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L644' href='#L644'><pre>644</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L645' href='#L645'><pre>645</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L646' href='#L646'><pre>646</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;BoolWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L647' href='#L647'><pre>647</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L648' href='#L648'><pre>648</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L649' href='#L649'><pre>649</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L650' href='#L650'><pre>650</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L651' href='#L651'><pre>651</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L652' href='#L652'><pre>652</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for BoolWrapper&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L653' href='#L653'><pre>653</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L654' href='#L654'><pre>654</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;BoolWrapper&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L655' href='#L655'><pre>655</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;value&quot;, &amp;self.value());</span></pre></td></tr><tr><td class='line-number'><a name='L656' href='#L656'><pre>656</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L657' href='#L657'><pre>657</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L658' href='#L658'><pre>658</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L659' href='#L659'><pre>659</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum FBSResourceInfoResultCallBackOffset {}</pre></td></tr><tr><td class='line-number'><a name='L660' href='#L660'><pre>660</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L661' href='#L661'><pre>661</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L662' href='#L662'><pre>662</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L663' href='#L663'><pre>663</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L664' href='#L664'><pre>664</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L665' href='#L665'><pre>665</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L666' href='#L666'><pre>666</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceInfoResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L667' href='#L667'><pre>667</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = FBSResourceInfoResultCallBack&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L668' href='#L668'><pre>668</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L669' href='#L669'><pre>669</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L670' href='#L670'><pre>670</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L671' href='#L671'><pre>671</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L672' href='#L672'><pre>672</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L673' href='#L673'><pre>673</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L674' href='#L674'><pre>674</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; FBSResourceInfoResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L675' href='#L675'><pre>675</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_INFO: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L676' href='#L676'><pre>676</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_MESSAGE: flatbuffers::VOffsetT = 6;</pre></td></tr><tr><td class='line-number'><a name='L677' href='#L677'><pre>677</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L678' href='#L678'><pre>678</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L679' href='#L679'><pre>679</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L680' href='#L680'><pre>680</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoResultCallBack { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L681' href='#L681'><pre>681</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L682' href='#L682'><pre>682</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L683' href='#L683'><pre>683</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L684' href='#L684'><pre>684</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L685' href='#L685'><pre>685</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args FBSResourceInfoResultCallBackArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L686' href='#L686'><pre>686</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoResultCallBack&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L687' href='#L687'><pre>687</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FBSResourceInfoResultCallBackBuilder::new(_fbb)</span>;</pre></td></tr><tr><td class='line-number'><a name='L688' href='#L688'><pre>688</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.message</span> <span class='region red'>{ builder.add_message(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L689' href='#L689'><pre>689</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.info</span> <span class='region red'>{ builder.add_info(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L690' href='#L690'><pre>690</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L691' href='#L691'><pre>691</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L692' href='#L692'><pre>692</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L693' href='#L693'><pre>693</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L694' href='#L694'><pre>694</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L695' href='#L695'><pre>695</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn info(&amp;self) -&gt; Option&lt;FBSResourceInfo&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L696' href='#L696'><pre>696</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L697' href='#L697'><pre>697</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L698' href='#L698'><pre>698</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L699' href='#L699'><pre>699</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;(FBSResourceInfoResultCallBack::VT_INFO, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L700' href='#L700'><pre>700</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L701' href='#L701'><pre>701</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L702' href='#L702'><pre>702</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn message(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L703' href='#L703'><pre>703</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L704' href='#L704'><pre>704</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L705' href='#L705'><pre>705</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L706' href='#L706'><pre>706</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfoResultCallBack::VT_MESSAGE, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L707' href='#L707'><pre>707</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L708' href='#L708'><pre>708</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L709' href='#L709'><pre>709</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L710' href='#L710'><pre>710</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for FBSResourceInfoResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L711' href='#L711'><pre>711</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L712' href='#L712'><pre>712</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L713' href='#L713'><pre>713</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L714' href='#L714'><pre>714</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L715' href='#L715'><pre>715</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L716' href='#L716'><pre>716</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L717' href='#L717'><pre>717</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;(&quot;info&quot;, Self::VT_INFO, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L718' href='#L718'><pre>718</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;message&quot;, Self::VT_MESSAGE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L719' href='#L719'><pre>719</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L720' href='#L720'><pre>720</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L721' href='#L721'><pre>721</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L722' href='#L722'><pre>722</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L723' href='#L723'><pre>723</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L724' href='#L724'><pre>724</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub info: Option&lt;flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L725' href='#L725'><pre>725</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub message: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L726' href='#L726'><pre>726</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L727' href='#L727'><pre>727</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for FBSResourceInfoResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L728' href='#L728'><pre>728</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L729' href='#L729'><pre>729</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L730' href='#L730'><pre>730</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L731' href='#L731'><pre>731</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      info: None,</span></pre></td></tr><tr><td class='line-number'><a name='L732' href='#L732'><pre>732</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      message: None,</span></pre></td></tr><tr><td class='line-number'><a name='L733' href='#L733'><pre>733</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L734' href='#L734'><pre>734</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L735' href='#L735'><pre>735</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L736' href='#L736'><pre>736</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L737' href='#L737'><pre>737</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoResultCallBackBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L738' href='#L738'><pre>738</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L739' href='#L739'><pre>739</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L740' href='#L740'><pre>740</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L741' href='#L741'><pre>741</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; FBSResourceInfoResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L742' href='#L742'><pre>742</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L743' href='#L743'><pre>743</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_info(&amp;mut self, info: flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;b &gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L744' href='#L744'><pre>744</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;FBSResourceInfo&gt;&gt;(FBSResourceInfoResultCallBack::VT_INFO, info);</span></pre></td></tr><tr><td class='line-number'><a name='L745' href='#L745'><pre>745</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L746' href='#L746'><pre>746</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L747' href='#L747'><pre>747</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_message(&amp;mut self, message: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L748' href='#L748'><pre>748</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfoResultCallBack::VT_MESSAGE, message);</span></pre></td></tr><tr><td class='line-number'><a name='L749' href='#L749'><pre>749</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L750' href='#L750'><pre>750</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L751' href='#L751'><pre>751</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; FBSResourceInfoResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L752' href='#L752'><pre>752</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L753' href='#L753'><pre>753</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoResultCallBackBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L754' href='#L754'><pre>754</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L755' href='#L755'><pre>755</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L756' href='#L756'><pre>756</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L757' href='#L757'><pre>757</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L758' href='#L758'><pre>758</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L759' href='#L759'><pre>759</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L760' href='#L760'><pre>760</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L761' href='#L761'><pre>761</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L762' href='#L762'><pre>762</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L763' href='#L763'><pre>763</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L764' href='#L764'><pre>764</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L765' href='#L765'><pre>765</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceInfoResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L766' href='#L766'><pre>766</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L767' href='#L767'><pre>767</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;FBSResourceInfoResultCallBack&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L768' href='#L768'><pre>768</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;info&quot;, &amp;self.info());</span></pre></td></tr><tr><td class='line-number'><a name='L769' href='#L769'><pre>769</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;message&quot;, &amp;self.message());</span></pre></td></tr><tr><td class='line-number'><a name='L770' href='#L770'><pre>770</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L771' href='#L771'><pre>771</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L772' href='#L772'><pre>772</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L773' href='#L773'><pre>773</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum FBSResourceInfoListResultCallBackOffset {}</pre></td></tr><tr><td class='line-number'><a name='L774' href='#L774'><pre>774</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L775' href='#L775'><pre>775</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L776' href='#L776'><pre>776</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoListResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L777' href='#L777'><pre>777</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L778' href='#L778'><pre>778</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L779' href='#L779'><pre>779</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L780' href='#L780'><pre>780</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceInfoListResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L781' href='#L781'><pre>781</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = FBSResourceInfoListResultCallBack&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L782' href='#L782'><pre>782</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L783' href='#L783'><pre>783</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L784' href='#L784'><pre>784</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L785' href='#L785'><pre>785</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L786' href='#L786'><pre>786</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L787' href='#L787'><pre>787</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L788' href='#L788'><pre>788</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; FBSResourceInfoListResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L789' href='#L789'><pre>789</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_INFO_LIST: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L790' href='#L790'><pre>790</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_MESSAGE: flatbuffers::VOffsetT = 6;</pre></td></tr><tr><td class='line-number'><a name='L791' href='#L791'><pre>791</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L792' href='#L792'><pre>792</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L793' href='#L793'><pre>793</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L794' href='#L794'><pre>794</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoListResultCallBack { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L795' href='#L795'><pre>795</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L796' href='#L796'><pre>796</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L797' href='#L797'><pre>797</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L798' href='#L798'><pre>798</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L799' href='#L799'><pre>799</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args FBSResourceInfoListResultCallBackArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L800' href='#L800'><pre>800</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoListResultCallBack&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L801' href='#L801'><pre>801</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FBSResourceInfoListResultCallBackBuilder::new(_fbb)</span>;</pre></td></tr><tr><td class='line-number'><a name='L802' href='#L802'><pre>802</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.message</span> <span class='region red'>{ builder.add_message(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L803' href='#L803'><pre>803</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.info_list</span> <span class='region red'>{ builder.add_info_list(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L804' href='#L804'><pre>804</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L805' href='#L805'><pre>805</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L806' href='#L806'><pre>806</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L807' href='#L807'><pre>807</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L808' href='#L808'><pre>808</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L809' href='#L809'><pre>809</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn info_list(&amp;self) -&gt; Option&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L810' href='#L810'><pre>810</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L811' href='#L811'><pre>811</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L812' href='#L812'><pre>812</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L813' href='#L813'><pre>813</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;&gt;&gt;(FBSResourceInfoListResultCallBack::VT_INFO_LIST, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L814' href='#L814'><pre>814</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L815' href='#L815'><pre>815</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L816' href='#L816'><pre>816</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn message(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L817' href='#L817'><pre>817</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L818' href='#L818'><pre>818</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L819' href='#L819'><pre>819</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L820' href='#L820'><pre>820</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfoListResultCallBack::VT_MESSAGE, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L821' href='#L821'><pre>821</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L822' href='#L822'><pre>822</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L823' href='#L823'><pre>823</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L824' href='#L824'><pre>824</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for FBSResourceInfoListResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L825' href='#L825'><pre>825</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L826' href='#L826'><pre>826</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L827' href='#L827'><pre>827</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L828' href='#L828'><pre>828</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L829' href='#L829'><pre>829</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L830' href='#L830'><pre>830</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L831' href='#L831'><pre>831</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;flatbuffers::Vector&lt;&apos;_, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;&gt;&gt;(&quot;info_list&quot;, Self::VT_INFO_LIST, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L832' href='#L832'><pre>832</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;message&quot;, Self::VT_MESSAGE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L833' href='#L833'><pre>833</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L834' href='#L834'><pre>834</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L835' href='#L835'><pre>835</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L836' href='#L836'><pre>836</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L837' href='#L837'><pre>837</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoListResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L838' href='#L838'><pre>838</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub info_list: Option&lt;flatbuffers::WIPOffset&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L839' href='#L839'><pre>839</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub message: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L840' href='#L840'><pre>840</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L841' href='#L841'><pre>841</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for FBSResourceInfoListResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L842' href='#L842'><pre>842</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L843' href='#L843'><pre>843</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L844' href='#L844'><pre>844</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoListResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L845' href='#L845'><pre>845</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      info_list: None,</span></pre></td></tr><tr><td class='line-number'><a name='L846' href='#L846'><pre>846</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      message: None,</span></pre></td></tr><tr><td class='line-number'><a name='L847' href='#L847'><pre>847</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L848' href='#L848'><pre>848</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L849' href='#L849'><pre>849</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L850' href='#L850'><pre>850</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L851' href='#L851'><pre>851</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoListResultCallBackBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L852' href='#L852'><pre>852</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L853' href='#L853'><pre>853</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L854' href='#L854'><pre>854</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L855' href='#L855'><pre>855</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; FBSResourceInfoListResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L856' href='#L856'><pre>856</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L857' href='#L857'><pre>857</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_info_list(&amp;mut self, info_list: flatbuffers::WIPOffset&lt;flatbuffers::Vector&lt;&apos;b , flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;b &gt;&gt;&gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L858' href='#L858'><pre>858</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfoListResultCallBack::VT_INFO_LIST, info_list);</span></pre></td></tr><tr><td class='line-number'><a name='L859' href='#L859'><pre>859</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L860' href='#L860'><pre>860</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L861' href='#L861'><pre>861</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_message(&amp;mut self, message: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L862' href='#L862'><pre>862</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfoListResultCallBack::VT_MESSAGE, message);</span></pre></td></tr><tr><td class='line-number'><a name='L863' href='#L863'><pre>863</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L864' href='#L864'><pre>864</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L865' href='#L865'><pre>865</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; FBSResourceInfoListResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L866' href='#L866'><pre>866</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L867' href='#L867'><pre>867</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoListResultCallBackBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L868' href='#L868'><pre>868</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L869' href='#L869'><pre>869</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L870' href='#L870'><pre>870</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L871' href='#L871'><pre>871</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L872' href='#L872'><pre>872</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L873' href='#L873'><pre>873</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoListResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L874' href='#L874'><pre>874</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L875' href='#L875'><pre>875</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L876' href='#L876'><pre>876</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L877' href='#L877'><pre>877</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L878' href='#L878'><pre>878</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L879' href='#L879'><pre>879</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceInfoListResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L880' href='#L880'><pre>880</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L881' href='#L881'><pre>881</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;FBSResourceInfoListResultCallBack&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L882' href='#L882'><pre>882</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;info_list&quot;, &amp;self.info_list());</span></pre></td></tr><tr><td class='line-number'><a name='L883' href='#L883'><pre>883</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;message&quot;, &amp;self.message());</span></pre></td></tr><tr><td class='line-number'><a name='L884' href='#L884'><pre>884</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L885' href='#L885'><pre>885</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L886' href='#L886'><pre>886</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L887' href='#L887'><pre>887</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum FBSResourceInfoOnProgressResultCallBackOffset {}</pre></td></tr><tr><td class='line-number'><a name='L888' href='#L888'><pre>888</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L889' href='#L889'><pre>889</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L890' href='#L890'><pre>890</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L891' href='#L891'><pre>891</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L892' href='#L892'><pre>892</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L893' href='#L893'><pre>893</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L894' href='#L894'><pre>894</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L895' href='#L895'><pre>895</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L896' href='#L896'><pre>896</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L897' href='#L897'><pre>897</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L898' href='#L898'><pre>898</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L899' href='#L899'><pre>899</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L900' href='#L900'><pre>900</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L901' href='#L901'><pre>901</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L902' href='#L902'><pre>902</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L903' href='#L903'><pre>903</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_INFO: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L904' href='#L904'><pre>904</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_PROGRESS: flatbuffers::VOffsetT = 6;</pre></td></tr><tr><td class='line-number'><a name='L905' href='#L905'><pre>905</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L906' href='#L906'><pre>906</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L907' href='#L907'><pre>907</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L908' href='#L908'><pre>908</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoOnProgressResultCallBack { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L909' href='#L909'><pre>909</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L910' href='#L910'><pre>910</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L911' href='#L911'><pre>911</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L912' href='#L912'><pre>912</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L913' href='#L913'><pre>913</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args FBSResourceInfoOnProgressResultCallBackArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L914' href='#L914'><pre>914</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoOnProgressResultCallBack&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L915' href='#L915'><pre>915</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FBSResourceInfoOnProgressResultCallBackBuilder::new(_fbb);</span></pre></td></tr><tr><td class='line-number'><a name='L916' href='#L916'><pre>916</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_progress(args.progress)</span>;</pre></td></tr><tr><td class='line-number'><a name='L917' href='#L917'><pre>917</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.info</span> <span class='region red'>{ builder.add_info(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L918' href='#L918'><pre>918</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L919' href='#L919'><pre>919</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L920' href='#L920'><pre>920</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L921' href='#L921'><pre>921</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L922' href='#L922'><pre>922</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L923' href='#L923'><pre>923</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn info(&amp;self) -&gt; Option&lt;FBSResourceInfo&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L924' href='#L924'><pre>924</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L925' href='#L925'><pre>925</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L926' href='#L926'><pre>926</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L927' href='#L927'><pre>927</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;(FBSResourceInfoOnProgressResultCallBack::VT_INFO, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L928' href='#L928'><pre>928</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L929' href='#L929'><pre>929</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L930' href='#L930'><pre>930</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn progress(&amp;self) -&gt; i32 {</span></pre></td></tr><tr><td class='line-number'><a name='L931' href='#L931'><pre>931</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L932' href='#L932'><pre>932</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L933' href='#L933'><pre>933</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L934' href='#L934'><pre>934</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i32&gt;(FBSResourceInfoOnProgressResultCallBack::VT_PROGRESS, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L935' href='#L935'><pre>935</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L936' href='#L936'><pre>936</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L937' href='#L937'><pre>937</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L938' href='#L938'><pre>938</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for FBSResourceInfoOnProgressResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L939' href='#L939'><pre>939</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L940' href='#L940'><pre>940</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L941' href='#L941'><pre>941</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L942' href='#L942'><pre>942</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L943' href='#L943'><pre>943</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L944' href='#L944'><pre>944</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L945' href='#L945'><pre>945</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;(&quot;info&quot;, Self::VT_INFO, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L946' href='#L946'><pre>946</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i32&gt;(&quot;progress&quot;, Self::VT_PROGRESS, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L947' href='#L947'><pre>947</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L948' href='#L948'><pre>948</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L949' href='#L949'><pre>949</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L950' href='#L950'><pre>950</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L951' href='#L951'><pre>951</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoOnProgressResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L952' href='#L952'><pre>952</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub info: Option&lt;flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L953' href='#L953'><pre>953</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub progress: i32,</pre></td></tr><tr><td class='line-number'><a name='L954' href='#L954'><pre>954</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L955' href='#L955'><pre>955</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for FBSResourceInfoOnProgressResultCallBackArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L956' href='#L956'><pre>956</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L957' href='#L957'><pre>957</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L958' href='#L958'><pre>958</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoOnProgressResultCallBackArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L959' href='#L959'><pre>959</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      info: None,</span></pre></td></tr><tr><td class='line-number'><a name='L960' href='#L960'><pre>960</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      progress: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L961' href='#L961'><pre>961</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L962' href='#L962'><pre>962</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L963' href='#L963'><pre>963</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L964' href='#L964'><pre>964</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L965' href='#L965'><pre>965</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoOnProgressResultCallBackBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L966' href='#L966'><pre>966</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L967' href='#L967'><pre>967</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L968' href='#L968'><pre>968</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L969' href='#L969'><pre>969</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; FBSResourceInfoOnProgressResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L970' href='#L970'><pre>970</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L971' href='#L971'><pre>971</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_info(&amp;mut self, info: flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;b &gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L972' href='#L972'><pre>972</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;FBSResourceInfo&gt;&gt;(FBSResourceInfoOnProgressResultCallBack::VT_INFO, info);</span></pre></td></tr><tr><td class='line-number'><a name='L973' href='#L973'><pre>973</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L974' href='#L974'><pre>974</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L975' href='#L975'><pre>975</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_progress(&amp;mut self, progress: i32) {</span></pre></td></tr><tr><td class='line-number'><a name='L976' href='#L976'><pre>976</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i32&gt;(FBSResourceInfoOnProgressResultCallBack::VT_PROGRESS, progress, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L977' href='#L977'><pre>977</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L978' href='#L978'><pre>978</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L979' href='#L979'><pre>979</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; FBSResourceInfoOnProgressResultCallBackBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L980' href='#L980'><pre>980</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L981' href='#L981'><pre>981</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoOnProgressResultCallBackBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L982' href='#L982'><pre>982</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L983' href='#L983'><pre>983</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L984' href='#L984'><pre>984</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L985' href='#L985'><pre>985</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L986' href='#L986'><pre>986</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L987' href='#L987'><pre>987</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L988' href='#L988'><pre>988</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L989' href='#L989'><pre>989</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L990' href='#L990'><pre>990</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L991' href='#L991'><pre>991</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L992' href='#L992'><pre>992</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L993' href='#L993'><pre>993</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceInfoOnProgressResultCallBack&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L994' href='#L994'><pre>994</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L995' href='#L995'><pre>995</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;FBSResourceInfoOnProgressResultCallBack&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L996' href='#L996'><pre>996</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;info&quot;, &amp;self.info());</span></pre></td></tr><tr><td class='line-number'><a name='L997' href='#L997'><pre>997</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;progress&quot;, &amp;self.progress());</span></pre></td></tr><tr><td class='line-number'><a name='L998' href='#L998'><pre>998</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L999' href='#L999'><pre>999</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1000' href='#L1000'><pre>1000</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1001' href='#L1001'><pre>1001</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum FBSResourceInfoOffset {}</pre></td></tr><tr><td class='line-number'><a name='L1002' href='#L1002'><pre>1002</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L1003' href='#L1003'><pre>1003</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1004' href='#L1004'><pre>1004</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfo&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1005' href='#L1005'><pre>1005</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1006' href='#L1006'><pre>1006</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1007' href='#L1007'><pre>1007</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1008' href='#L1008'><pre>1008</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceInfo&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1009' href='#L1009'><pre>1009</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = FBSResourceInfo&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L1010' href='#L1010'><pre>1010</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1011' href='#L1011'><pre>1011</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L1012' href='#L1012'><pre>1012</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L1013' href='#L1013'><pre>1013</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1014' href='#L1014'><pre>1014</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1015' href='#L1015'><pre>1015</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1016' href='#L1016'><pre>1016</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; FBSResourceInfo&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1017' href='#L1017'><pre>1017</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_ID: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L1018' href='#L1018'><pre>1018</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_NAME: flatbuffers::VOffsetT = 6;</pre></td></tr><tr><td class='line-number'><a name='L1019' href='#L1019'><pre>1019</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_VERSION: flatbuffers::VOffsetT = 8;</pre></td></tr><tr><td class='line-number'><a name='L1020' href='#L1020'><pre>1020</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_RESOURCE_TYPE: flatbuffers::VOffsetT = 10;</pre></td></tr><tr><td class='line-number'><a name='L1021' href='#L1021'><pre>1021</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_CREATE_TIME: flatbuffers::VOffsetT = 12;</pre></td></tr><tr><td class='line-number'><a name='L1022' href='#L1022'><pre>1022</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_UPDATE_TIME: flatbuffers::VOffsetT = 14;</pre></td></tr><tr><td class='line-number'><a name='L1023' href='#L1023'><pre>1023</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_DOWNLOAD_URL: flatbuffers::VOffsetT = 16;</pre></td></tr><tr><td class='line-number'><a name='L1024' href='#L1024'><pre>1024</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_PATH: flatbuffers::VOffsetT = 18;</pre></td></tr><tr><td class='line-number'><a name='L1025' href='#L1025'><pre>1025</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_INDEX_PATH: flatbuffers::VOffsetT = 20;</pre></td></tr><tr><td class='line-number'><a name='L1026' href='#L1026'><pre>1026</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_IS_PRESET: flatbuffers::VOffsetT = 22;</pre></td></tr><tr><td class='line-number'><a name='L1027' href='#L1027'><pre>1027</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_IS_ACTIVE: flatbuffers::VOffsetT = 24;</pre></td></tr><tr><td class='line-number'><a name='L1028' href='#L1028'><pre>1028</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_IS_SERVER_LATEST: flatbuffers::VOffsetT = 26;</pre></td></tr><tr><td class='line-number'><a name='L1029' href='#L1029'><pre>1029</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_HIDE_STATUS_BAR: flatbuffers::VOffsetT = 28;</pre></td></tr><tr><td class='line-number'><a name='L1030' href='#L1030'><pre>1030</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_IS_FORCE_UPGRADE: flatbuffers::VOffsetT = 30;</pre></td></tr><tr><td class='line-number'><a name='L1031' href='#L1031'><pre>1031</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_RULES_JSON: flatbuffers::VOffsetT = 32;</pre></td></tr><tr><td class='line-number'><a name='L1032' href='#L1032'><pre>1032</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_REMOTE_PAGE_URL: flatbuffers::VOffsetT = 34;</pre></td></tr><tr><td class='line-number'><a name='L1033' href='#L1033'><pre>1033</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_STATUS: flatbuffers::VOffsetT = 36;</pre></td></tr><tr><td class='line-number'><a name='L1034' href='#L1034'><pre>1034</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_HASH: flatbuffers::VOffsetT = 38;</pre></td></tr><tr><td class='line-number'><a name='L1035' href='#L1035'><pre>1035</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_DOWNLOAD_PRIORITY: flatbuffers::VOffsetT = 40;</pre></td></tr><tr><td class='line-number'><a name='L1036' href='#L1036'><pre>1036</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1037' href='#L1037'><pre>1037</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1038' href='#L1038'><pre>1038</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1039' href='#L1039'><pre>1039</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfo { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L1040' href='#L1040'><pre>1040</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1041' href='#L1041'><pre>1041</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L1042' href='#L1042'><pre>1042</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1043' href='#L1043'><pre>1043</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L1044' href='#L1044'><pre>1044</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args FBSResourceInfoArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L1045' href='#L1045'><pre>1045</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1046' href='#L1046'><pre>1046</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FBSResourceInfoBuilder::new(_fbb);</span></pre></td></tr><tr><td class='line-number'><a name='L1047' href='#L1047'><pre>1047</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_update_time(args.update_time);</span></pre></td></tr><tr><td class='line-number'><a name='L1048' href='#L1048'><pre>1048</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_create_time(args.create_time);</span></pre></td></tr><tr><td class='line-number'><a name='L1049' href='#L1049'><pre>1049</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_id(args.id)</span>;</pre></td></tr><tr><td class='line-number'><a name='L1050' href='#L1050'><pre>1050</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.hash</span> <span class='region red'>{ builder.add_hash(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1051' href='#L1051'><pre>1051</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.add_status(args.status)</span>;</pre></td></tr><tr><td class='line-number'><a name='L1052' href='#L1052'><pre>1052</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.remote_page_url</span> <span class='region red'>{ builder.add_remote_page_url(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1053' href='#L1053'><pre>1053</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.rules_json</span> <span class='region red'>{ builder.add_rules_json(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1054' href='#L1054'><pre>1054</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.index_path</span> <span class='region red'>{ builder.add_index_path(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1055' href='#L1055'><pre>1055</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.path</span> <span class='region red'>{ builder.add_path(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1056' href='#L1056'><pre>1056</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.download_url</span> <span class='region red'>{ builder.add_download_url(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1057' href='#L1057'><pre>1057</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.version</span> <span class='region red'>{ builder.add_version(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1058' href='#L1058'><pre>1058</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.name</span> <span class='region red'>{ builder.add_name(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1059' href='#L1059'><pre>1059</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.add_download_priority(args.download_priority);</span></pre></td></tr><tr><td class='line-number'><a name='L1060' href='#L1060'><pre>1060</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_is_force_upgrade(args.is_force_upgrade);</span></pre></td></tr><tr><td class='line-number'><a name='L1061' href='#L1061'><pre>1061</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_hide_status_bar(args.hide_status_bar);</span></pre></td></tr><tr><td class='line-number'><a name='L1062' href='#L1062'><pre>1062</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_is_server_latest(args.is_server_latest);</span></pre></td></tr><tr><td class='line-number'><a name='L1063' href='#L1063'><pre>1063</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_is_active(args.is_active);</span></pre></td></tr><tr><td class='line-number'><a name='L1064' href='#L1064'><pre>1064</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_is_preset(args.is_preset);</span></pre></td></tr><tr><td class='line-number'><a name='L1065' href='#L1065'><pre>1065</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.add_resource_type(args.resource_type);</span></pre></td></tr><tr><td class='line-number'><a name='L1066' href='#L1066'><pre>1066</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1067' href='#L1067'><pre>1067</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1068' href='#L1068'><pre>1068</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1069' href='#L1069'><pre>1069</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1070' href='#L1070'><pre>1070</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1071' href='#L1071'><pre>1071</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn id(&amp;self) -&gt; i64 {</span></pre></td></tr><tr><td class='line-number'><a name='L1072' href='#L1072'><pre>1072</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1073' href='#L1073'><pre>1073</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1074' href='#L1074'><pre>1074</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1075' href='#L1075'><pre>1075</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i64&gt;(FBSResourceInfo::VT_ID, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1076' href='#L1076'><pre>1076</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1077' href='#L1077'><pre>1077</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1078' href='#L1078'><pre>1078</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn name(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1079' href='#L1079'><pre>1079</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1080' href='#L1080'><pre>1080</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1081' href='#L1081'><pre>1081</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1082' href='#L1082'><pre>1082</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_NAME, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1083' href='#L1083'><pre>1083</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1084' href='#L1084'><pre>1084</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1085' href='#L1085'><pre>1085</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn version(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1086' href='#L1086'><pre>1086</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1087' href='#L1087'><pre>1087</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1088' href='#L1088'><pre>1088</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1089' href='#L1089'><pre>1089</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_VERSION, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1090' href='#L1090'><pre>1090</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1091' href='#L1091'><pre>1091</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1092' href='#L1092'><pre>1092</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn resource_type(&amp;self) -&gt; FBSResourceType {</span></pre></td></tr><tr><td class='line-number'><a name='L1093' href='#L1093'><pre>1093</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1094' href='#L1094'><pre>1094</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1095' href='#L1095'><pre>1095</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1096' href='#L1096'><pre>1096</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;FBSResourceType&gt;(FBSResourceInfo::VT_RESOURCE_TYPE, Some(FBSResourceType::DeviceConfig)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1097' href='#L1097'><pre>1097</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1098' href='#L1098'><pre>1098</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1099' href='#L1099'><pre>1099</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create_time(&amp;self) -&gt; i64 {</span></pre></td></tr><tr><td class='line-number'><a name='L1100' href='#L1100'><pre>1100</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1101' href='#L1101'><pre>1101</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1102' href='#L1102'><pre>1102</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1103' href='#L1103'><pre>1103</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i64&gt;(FBSResourceInfo::VT_CREATE_TIME, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1104' href='#L1104'><pre>1104</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1105' href='#L1105'><pre>1105</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1106' href='#L1106'><pre>1106</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn update_time(&amp;self) -&gt; i64 {</span></pre></td></tr><tr><td class='line-number'><a name='L1107' href='#L1107'><pre>1107</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1108' href='#L1108'><pre>1108</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1109' href='#L1109'><pre>1109</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1110' href='#L1110'><pre>1110</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i64&gt;(FBSResourceInfo::VT_UPDATE_TIME, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1111' href='#L1111'><pre>1111</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1112' href='#L1112'><pre>1112</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1113' href='#L1113'><pre>1113</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn download_url(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1114' href='#L1114'><pre>1114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1115' href='#L1115'><pre>1115</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1116' href='#L1116'><pre>1116</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1117' href='#L1117'><pre>1117</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_DOWNLOAD_URL, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1118' href='#L1118'><pre>1118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1119' href='#L1119'><pre>1119</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1120' href='#L1120'><pre>1120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn path(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1121' href='#L1121'><pre>1121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1122' href='#L1122'><pre>1122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1123' href='#L1123'><pre>1123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1124' href='#L1124'><pre>1124</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_PATH, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1125' href='#L1125'><pre>1125</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1126' href='#L1126'><pre>1126</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1127' href='#L1127'><pre>1127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn index_path(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1128' href='#L1128'><pre>1128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1129' href='#L1129'><pre>1129</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1130' href='#L1130'><pre>1130</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1131' href='#L1131'><pre>1131</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_INDEX_PATH, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1132' href='#L1132'><pre>1132</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1133' href='#L1133'><pre>1133</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1134' href='#L1134'><pre>1134</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn is_preset(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L1135' href='#L1135'><pre>1135</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1136' href='#L1136'><pre>1136</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1137' href='#L1137'><pre>1137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1138' href='#L1138'><pre>1138</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(FBSResourceInfo::VT_IS_PRESET, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1139' href='#L1139'><pre>1139</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1140' href='#L1140'><pre>1140</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1141' href='#L1141'><pre>1141</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn is_active(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L1142' href='#L1142'><pre>1142</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1143' href='#L1143'><pre>1143</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1144' href='#L1144'><pre>1144</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1145' href='#L1145'><pre>1145</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(FBSResourceInfo::VT_IS_ACTIVE, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1146' href='#L1146'><pre>1146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1147' href='#L1147'><pre>1147</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1148' href='#L1148'><pre>1148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn is_server_latest(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L1149' href='#L1149'><pre>1149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1150' href='#L1150'><pre>1150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1151' href='#L1151'><pre>1151</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1152' href='#L1152'><pre>1152</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(FBSResourceInfo::VT_IS_SERVER_LATEST, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1153' href='#L1153'><pre>1153</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1154' href='#L1154'><pre>1154</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1155' href='#L1155'><pre>1155</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn hide_status_bar(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L1156' href='#L1156'><pre>1156</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1157' href='#L1157'><pre>1157</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1158' href='#L1158'><pre>1158</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1159' href='#L1159'><pre>1159</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(FBSResourceInfo::VT_HIDE_STATUS_BAR, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1160' href='#L1160'><pre>1160</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1161' href='#L1161'><pre>1161</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1162' href='#L1162'><pre>1162</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn is_force_upgrade(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L1163' href='#L1163'><pre>1163</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1164' href='#L1164'><pre>1164</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1165' href='#L1165'><pre>1165</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1166' href='#L1166'><pre>1166</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;bool&gt;(FBSResourceInfo::VT_IS_FORCE_UPGRADE, Some(false)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1167' href='#L1167'><pre>1167</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1168' href='#L1168'><pre>1168</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1169' href='#L1169'><pre>1169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn rules_json(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1170' href='#L1170'><pre>1170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1171' href='#L1171'><pre>1171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1172' href='#L1172'><pre>1172</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1173' href='#L1173'><pre>1173</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_RULES_JSON, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1174' href='#L1174'><pre>1174</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1175' href='#L1175'><pre>1175</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1176' href='#L1176'><pre>1176</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn remote_page_url(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1177' href='#L1177'><pre>1177</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1178' href='#L1178'><pre>1178</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1179' href='#L1179'><pre>1179</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1180' href='#L1180'><pre>1180</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_REMOTE_PAGE_URL, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1181' href='#L1181'><pre>1181</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1182' href='#L1182'><pre>1182</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1183' href='#L1183'><pre>1183</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn status(&amp;self) -&gt; i32 {</span></pre></td></tr><tr><td class='line-number'><a name='L1184' href='#L1184'><pre>1184</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1185' href='#L1185'><pre>1185</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1186' href='#L1186'><pre>1186</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1187' href='#L1187'><pre>1187</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i32&gt;(FBSResourceInfo::VT_STATUS, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1188' href='#L1188'><pre>1188</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1189' href='#L1189'><pre>1189</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1190' href='#L1190'><pre>1190</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn hash(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1191' href='#L1191'><pre>1191</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1192' href='#L1192'><pre>1192</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1193' href='#L1193'><pre>1193</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1194' href='#L1194'><pre>1194</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(FBSResourceInfo::VT_HASH, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1195' href='#L1195'><pre>1195</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1196' href='#L1196'><pre>1196</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1197' href='#L1197'><pre>1197</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn download_priority(&amp;self) -&gt; FBSResourceDownloadPriority {</span></pre></td></tr><tr><td class='line-number'><a name='L1198' href='#L1198'><pre>1198</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1199' href='#L1199'><pre>1199</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1200' href='#L1200'><pre>1200</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1201' href='#L1201'><pre>1201</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;FBSResourceDownloadPriority&gt;(FBSResourceInfo::VT_DOWNLOAD_PRIORITY, Some(FBSResourceDownloadPriority::Normal)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1202' href='#L1202'><pre>1202</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1203' href='#L1203'><pre>1203</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1204' href='#L1204'><pre>1204</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1205' href='#L1205'><pre>1205</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for FBSResourceInfo&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1206' href='#L1206'><pre>1206</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1207' href='#L1207'><pre>1207</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L1208' href='#L1208'><pre>1208</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L1209' href='#L1209'><pre>1209</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L1210' href='#L1210'><pre>1210</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L1211' href='#L1211'><pre>1211</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1212' href='#L1212'><pre>1212</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i64&gt;(&quot;id&quot;, Self::VT_ID, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1213' href='#L1213'><pre>1213</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;name&quot;, Self::VT_NAME, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1214' href='#L1214'><pre>1214</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;version&quot;, Self::VT_VERSION, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1215' href='#L1215'><pre>1215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;FBSResourceType&gt;(&quot;resource_type&quot;, Self::VT_RESOURCE_TYPE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1216' href='#L1216'><pre>1216</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i64&gt;(&quot;create_time&quot;, Self::VT_CREATE_TIME, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1217' href='#L1217'><pre>1217</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i64&gt;(&quot;update_time&quot;, Self::VT_UPDATE_TIME, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1218' href='#L1218'><pre>1218</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;download_url&quot;, Self::VT_DOWNLOAD_URL, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1219' href='#L1219'><pre>1219</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;path&quot;, Self::VT_PATH, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1220' href='#L1220'><pre>1220</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;index_path&quot;, Self::VT_INDEX_PATH, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1221' href='#L1221'><pre>1221</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;is_preset&quot;, Self::VT_IS_PRESET, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1222' href='#L1222'><pre>1222</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;is_active&quot;, Self::VT_IS_ACTIVE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1223' href='#L1223'><pre>1223</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;is_server_latest&quot;, Self::VT_IS_SERVER_LATEST, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1224' href='#L1224'><pre>1224</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;hide_status_bar&quot;, Self::VT_HIDE_STATUS_BAR, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1225' href='#L1225'><pre>1225</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;bool&gt;(&quot;is_force_upgrade&quot;, Self::VT_IS_FORCE_UPGRADE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1226' href='#L1226'><pre>1226</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;rules_json&quot;, Self::VT_RULES_JSON, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1227' href='#L1227'><pre>1227</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;remote_page_url&quot;, Self::VT_REMOTE_PAGE_URL, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1228' href='#L1228'><pre>1228</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i32&gt;(&quot;status&quot;, Self::VT_STATUS, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1229' href='#L1229'><pre>1229</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;hash&quot;, Self::VT_HASH, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1230' href='#L1230'><pre>1230</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;FBSResourceDownloadPriority&gt;(&quot;download_priority&quot;, Self::VT_DOWNLOAD_PRIORITY, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1231' href='#L1231'><pre>1231</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L1232' href='#L1232'><pre>1232</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L1233' href='#L1233'><pre>1233</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1234' href='#L1234'><pre>1234</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1235' href='#L1235'><pre>1235</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1236' href='#L1236'><pre>1236</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub id: i64,</pre></td></tr><tr><td class='line-number'><a name='L1237' href='#L1237'><pre>1237</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub name: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1238' href='#L1238'><pre>1238</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub version: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1239' href='#L1239'><pre>1239</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub resource_type: FBSResourceType,</pre></td></tr><tr><td class='line-number'><a name='L1240' href='#L1240'><pre>1240</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub create_time: i64,</pre></td></tr><tr><td class='line-number'><a name='L1241' href='#L1241'><pre>1241</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub update_time: i64,</pre></td></tr><tr><td class='line-number'><a name='L1242' href='#L1242'><pre>1242</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub download_url: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1243' href='#L1243'><pre>1243</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub path: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1244' href='#L1244'><pre>1244</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub index_path: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1245' href='#L1245'><pre>1245</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub is_preset: bool,</pre></td></tr><tr><td class='line-number'><a name='L1246' href='#L1246'><pre>1246</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub is_active: bool,</pre></td></tr><tr><td class='line-number'><a name='L1247' href='#L1247'><pre>1247</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub is_server_latest: bool,</pre></td></tr><tr><td class='line-number'><a name='L1248' href='#L1248'><pre>1248</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub hide_status_bar: bool,</pre></td></tr><tr><td class='line-number'><a name='L1249' href='#L1249'><pre>1249</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub is_force_upgrade: bool,</pre></td></tr><tr><td class='line-number'><a name='L1250' href='#L1250'><pre>1250</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub rules_json: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1251' href='#L1251'><pre>1251</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub remote_page_url: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1252' href='#L1252'><pre>1252</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub status: i32,</pre></td></tr><tr><td class='line-number'><a name='L1253' href='#L1253'><pre>1253</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub hash: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1254' href='#L1254'><pre>1254</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub download_priority: FBSResourceDownloadPriority,</pre></td></tr><tr><td class='line-number'><a name='L1255' href='#L1255'><pre>1255</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1256' href='#L1256'><pre>1256</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for FBSResourceInfoArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1257' href='#L1257'><pre>1257</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1258' href='#L1258'><pre>1258</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1259' href='#L1259'><pre>1259</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L1260' href='#L1260'><pre>1260</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      id: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L1261' href='#L1261'><pre>1261</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      name: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1262' href='#L1262'><pre>1262</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      version: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1263' href='#L1263'><pre>1263</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      resource_type: FBSResourceType::DeviceConfig,</span></pre></td></tr><tr><td class='line-number'><a name='L1264' href='#L1264'><pre>1264</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      create_time: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L1265' href='#L1265'><pre>1265</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      update_time: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L1266' href='#L1266'><pre>1266</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      download_url: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1267' href='#L1267'><pre>1267</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      path: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1268' href='#L1268'><pre>1268</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      index_path: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1269' href='#L1269'><pre>1269</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      is_preset: false,</span></pre></td></tr><tr><td class='line-number'><a name='L1270' href='#L1270'><pre>1270</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      is_active: false,</span></pre></td></tr><tr><td class='line-number'><a name='L1271' href='#L1271'><pre>1271</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      is_server_latest: false,</span></pre></td></tr><tr><td class='line-number'><a name='L1272' href='#L1272'><pre>1272</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      hide_status_bar: false,</span></pre></td></tr><tr><td class='line-number'><a name='L1273' href='#L1273'><pre>1273</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      is_force_upgrade: false,</span></pre></td></tr><tr><td class='line-number'><a name='L1274' href='#L1274'><pre>1274</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      rules_json: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1275' href='#L1275'><pre>1275</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      remote_page_url: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1276' href='#L1276'><pre>1276</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      status: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L1277' href='#L1277'><pre>1277</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      hash: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1278' href='#L1278'><pre>1278</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      download_priority: FBSResourceDownloadPriority::Normal,</span></pre></td></tr><tr><td class='line-number'><a name='L1279' href='#L1279'><pre>1279</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1280' href='#L1280'><pre>1280</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1281' href='#L1281'><pre>1281</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1282' href='#L1282'><pre>1282</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1283' href='#L1283'><pre>1283</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1284' href='#L1284'><pre>1284</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1285' href='#L1285'><pre>1285</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1286' href='#L1286'><pre>1286</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1287' href='#L1287'><pre>1287</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; FBSResourceInfoBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1288' href='#L1288'><pre>1288</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1289' href='#L1289'><pre>1289</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_id(&amp;mut self, id: i64) {</span></pre></td></tr><tr><td class='line-number'><a name='L1290' href='#L1290'><pre>1290</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i64&gt;(FBSResourceInfo::VT_ID, id, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L1291' href='#L1291'><pre>1291</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1292' href='#L1292'><pre>1292</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1293' href='#L1293'><pre>1293</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_name(&amp;mut self, name: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1294' href='#L1294'><pre>1294</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_NAME, name);</span></pre></td></tr><tr><td class='line-number'><a name='L1295' href='#L1295'><pre>1295</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1296' href='#L1296'><pre>1296</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1297' href='#L1297'><pre>1297</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_version(&amp;mut self, version: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1298' href='#L1298'><pre>1298</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_VERSION, version);</span></pre></td></tr><tr><td class='line-number'><a name='L1299' href='#L1299'><pre>1299</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1300' href='#L1300'><pre>1300</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1301' href='#L1301'><pre>1301</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_resource_type(&amp;mut self, resource_type: FBSResourceType) {</span></pre></td></tr><tr><td class='line-number'><a name='L1302' href='#L1302'><pre>1302</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;FBSResourceType&gt;(FBSResourceInfo::VT_RESOURCE_TYPE, resource_type, FBSResourceType::DeviceConfig);</span></pre></td></tr><tr><td class='line-number'><a name='L1303' href='#L1303'><pre>1303</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1304' href='#L1304'><pre>1304</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1305' href='#L1305'><pre>1305</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_create_time(&amp;mut self, create_time: i64) {</span></pre></td></tr><tr><td class='line-number'><a name='L1306' href='#L1306'><pre>1306</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i64&gt;(FBSResourceInfo::VT_CREATE_TIME, create_time, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L1307' href='#L1307'><pre>1307</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1308' href='#L1308'><pre>1308</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1309' href='#L1309'><pre>1309</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_update_time(&amp;mut self, update_time: i64) {</span></pre></td></tr><tr><td class='line-number'><a name='L1310' href='#L1310'><pre>1310</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i64&gt;(FBSResourceInfo::VT_UPDATE_TIME, update_time, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L1311' href='#L1311'><pre>1311</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1312' href='#L1312'><pre>1312</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1313' href='#L1313'><pre>1313</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_download_url(&amp;mut self, download_url: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1314' href='#L1314'><pre>1314</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_DOWNLOAD_URL, download_url);</span></pre></td></tr><tr><td class='line-number'><a name='L1315' href='#L1315'><pre>1315</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1316' href='#L1316'><pre>1316</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1317' href='#L1317'><pre>1317</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_path(&amp;mut self, path: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1318' href='#L1318'><pre>1318</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_PATH, path);</span></pre></td></tr><tr><td class='line-number'><a name='L1319' href='#L1319'><pre>1319</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1320' href='#L1320'><pre>1320</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1321' href='#L1321'><pre>1321</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_index_path(&amp;mut self, index_path: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1322' href='#L1322'><pre>1322</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_INDEX_PATH, index_path);</span></pre></td></tr><tr><td class='line-number'><a name='L1323' href='#L1323'><pre>1323</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1324' href='#L1324'><pre>1324</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1325' href='#L1325'><pre>1325</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_is_preset(&amp;mut self, is_preset: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L1326' href='#L1326'><pre>1326</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(FBSResourceInfo::VT_IS_PRESET, is_preset, false);</span></pre></td></tr><tr><td class='line-number'><a name='L1327' href='#L1327'><pre>1327</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1328' href='#L1328'><pre>1328</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1329' href='#L1329'><pre>1329</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_is_active(&amp;mut self, is_active: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L1330' href='#L1330'><pre>1330</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(FBSResourceInfo::VT_IS_ACTIVE, is_active, false);</span></pre></td></tr><tr><td class='line-number'><a name='L1331' href='#L1331'><pre>1331</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1332' href='#L1332'><pre>1332</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1333' href='#L1333'><pre>1333</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_is_server_latest(&amp;mut self, is_server_latest: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L1334' href='#L1334'><pre>1334</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(FBSResourceInfo::VT_IS_SERVER_LATEST, is_server_latest, false);</span></pre></td></tr><tr><td class='line-number'><a name='L1335' href='#L1335'><pre>1335</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1336' href='#L1336'><pre>1336</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1337' href='#L1337'><pre>1337</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_hide_status_bar(&amp;mut self, hide_status_bar: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L1338' href='#L1338'><pre>1338</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(FBSResourceInfo::VT_HIDE_STATUS_BAR, hide_status_bar, false);</span></pre></td></tr><tr><td class='line-number'><a name='L1339' href='#L1339'><pre>1339</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1340' href='#L1340'><pre>1340</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1341' href='#L1341'><pre>1341</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_is_force_upgrade(&amp;mut self, is_force_upgrade: bool) {</span></pre></td></tr><tr><td class='line-number'><a name='L1342' href='#L1342'><pre>1342</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;bool&gt;(FBSResourceInfo::VT_IS_FORCE_UPGRADE, is_force_upgrade, false);</span></pre></td></tr><tr><td class='line-number'><a name='L1343' href='#L1343'><pre>1343</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1344' href='#L1344'><pre>1344</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1345' href='#L1345'><pre>1345</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_rules_json(&amp;mut self, rules_json: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1346' href='#L1346'><pre>1346</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_RULES_JSON, rules_json);</span></pre></td></tr><tr><td class='line-number'><a name='L1347' href='#L1347'><pre>1347</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1348' href='#L1348'><pre>1348</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1349' href='#L1349'><pre>1349</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_remote_page_url(&amp;mut self, remote_page_url: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1350' href='#L1350'><pre>1350</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_REMOTE_PAGE_URL, remote_page_url);</span></pre></td></tr><tr><td class='line-number'><a name='L1351' href='#L1351'><pre>1351</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1352' href='#L1352'><pre>1352</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1353' href='#L1353'><pre>1353</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_status(&amp;mut self, status: i32) {</span></pre></td></tr><tr><td class='line-number'><a name='L1354' href='#L1354'><pre>1354</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i32&gt;(FBSResourceInfo::VT_STATUS, status, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L1355' href='#L1355'><pre>1355</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1356' href='#L1356'><pre>1356</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1357' href='#L1357'><pre>1357</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_hash(&amp;mut self, hash: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1358' href='#L1358'><pre>1358</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfo::VT_HASH, hash);</span></pre></td></tr><tr><td class='line-number'><a name='L1359' href='#L1359'><pre>1359</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1360' href='#L1360'><pre>1360</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1361' href='#L1361'><pre>1361</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_download_priority(&amp;mut self, download_priority: FBSResourceDownloadPriority) {</span></pre></td></tr><tr><td class='line-number'><a name='L1362' href='#L1362'><pre>1362</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;FBSResourceDownloadPriority&gt;(FBSResourceInfo::VT_DOWNLOAD_PRIORITY, download_priority, FBSResourceDownloadPriority::Normal);</span></pre></td></tr><tr><td class='line-number'><a name='L1363' href='#L1363'><pre>1363</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1364' href='#L1364'><pre>1364</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1365' href='#L1365'><pre>1365</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; FBSResourceInfoBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1366' href='#L1366'><pre>1366</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L1367' href='#L1367'><pre>1367</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L1368' href='#L1368'><pre>1368</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L1369' href='#L1369'><pre>1369</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L1370' href='#L1370'><pre>1370</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1371' href='#L1371'><pre>1371</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1372' href='#L1372'><pre>1372</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1373' href='#L1373'><pre>1373</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1374' href='#L1374'><pre>1374</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L1375' href='#L1375'><pre>1375</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L1376' href='#L1376'><pre>1376</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1377' href='#L1377'><pre>1377</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1378' href='#L1378'><pre>1378</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1379' href='#L1379'><pre>1379</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceInfo&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1380' href='#L1380'><pre>1380</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L1381' href='#L1381'><pre>1381</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;FBSResourceInfo&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L1382' href='#L1382'><pre>1382</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;id&quot;, &amp;self.id());</span></pre></td></tr><tr><td class='line-number'><a name='L1383' href='#L1383'><pre>1383</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;name&quot;, &amp;self.name());</span></pre></td></tr><tr><td class='line-number'><a name='L1384' href='#L1384'><pre>1384</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;version&quot;, &amp;self.version());</span></pre></td></tr><tr><td class='line-number'><a name='L1385' href='#L1385'><pre>1385</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;resource_type&quot;, &amp;self.resource_type());</span></pre></td></tr><tr><td class='line-number'><a name='L1386' href='#L1386'><pre>1386</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;create_time&quot;, &amp;self.create_time());</span></pre></td></tr><tr><td class='line-number'><a name='L1387' href='#L1387'><pre>1387</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;update_time&quot;, &amp;self.update_time());</span></pre></td></tr><tr><td class='line-number'><a name='L1388' href='#L1388'><pre>1388</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;download_url&quot;, &amp;self.download_url());</span></pre></td></tr><tr><td class='line-number'><a name='L1389' href='#L1389'><pre>1389</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;path&quot;, &amp;self.path());</span></pre></td></tr><tr><td class='line-number'><a name='L1390' href='#L1390'><pre>1390</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;index_path&quot;, &amp;self.index_path());</span></pre></td></tr><tr><td class='line-number'><a name='L1391' href='#L1391'><pre>1391</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;is_preset&quot;, &amp;self.is_preset());</span></pre></td></tr><tr><td class='line-number'><a name='L1392' href='#L1392'><pre>1392</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;is_active&quot;, &amp;self.is_active());</span></pre></td></tr><tr><td class='line-number'><a name='L1393' href='#L1393'><pre>1393</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;is_server_latest&quot;, &amp;self.is_server_latest());</span></pre></td></tr><tr><td class='line-number'><a name='L1394' href='#L1394'><pre>1394</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;hide_status_bar&quot;, &amp;self.hide_status_bar());</span></pre></td></tr><tr><td class='line-number'><a name='L1395' href='#L1395'><pre>1395</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;is_force_upgrade&quot;, &amp;self.is_force_upgrade());</span></pre></td></tr><tr><td class='line-number'><a name='L1396' href='#L1396'><pre>1396</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;rules_json&quot;, &amp;self.rules_json());</span></pre></td></tr><tr><td class='line-number'><a name='L1397' href='#L1397'><pre>1397</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;remote_page_url&quot;, &amp;self.remote_page_url());</span></pre></td></tr><tr><td class='line-number'><a name='L1398' href='#L1398'><pre>1398</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;status&quot;, &amp;self.status());</span></pre></td></tr><tr><td class='line-number'><a name='L1399' href='#L1399'><pre>1399</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;hash&quot;, &amp;self.hash());</span></pre></td></tr><tr><td class='line-number'><a name='L1400' href='#L1400'><pre>1400</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;download_priority&quot;, &amp;self.download_priority());</span></pre></td></tr><tr><td class='line-number'><a name='L1401' href='#L1401'><pre>1401</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1402' href='#L1402'><pre>1402</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1403' href='#L1403'><pre>1403</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1404' href='#L1404'><pre>1404</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum FBSResourceInfoListOffset {}</pre></td></tr><tr><td class='line-number'><a name='L1405' href='#L1405'><pre>1405</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L1406' href='#L1406'><pre>1406</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1407' href='#L1407'><pre>1407</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoList&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1408' href='#L1408'><pre>1408</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1409' href='#L1409'><pre>1409</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1410' href='#L1410'><pre>1410</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1411' href='#L1411'><pre>1411</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for FBSResourceInfoList&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1412' href='#L1412'><pre>1412</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = FBSResourceInfoList&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L1413' href='#L1413'><pre>1413</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1414' href='#L1414'><pre>1414</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L1415' href='#L1415'><pre>1415</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L1416' href='#L1416'><pre>1416</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1417' href='#L1417'><pre>1417</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1418' href='#L1418'><pre>1418</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1419' href='#L1419'><pre>1419</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; FBSResourceInfoList&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1420' href='#L1420'><pre>1420</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_RESOURCES: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L1421' href='#L1421'><pre>1421</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1422' href='#L1422'><pre>1422</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1423' href='#L1423'><pre>1423</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1424' href='#L1424'><pre>1424</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoList { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L1425' href='#L1425'><pre>1425</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1426' href='#L1426'><pre>1426</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L1427' href='#L1427'><pre>1427</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1428' href='#L1428'><pre>1428</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L1429' href='#L1429'><pre>1429</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args FBSResourceInfoListArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L1430' href='#L1430'><pre>1430</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoList&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1431' href='#L1431'><pre>1431</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = FBSResourceInfoListBuilder::new(_fbb)</span>;</pre></td></tr><tr><td class='line-number'><a name='L1432' href='#L1432'><pre>1432</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.resources</span> <span class='region red'>{ builder.add_resources(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1433' href='#L1433'><pre>1433</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1434' href='#L1434'><pre>1434</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1435' href='#L1435'><pre>1435</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1436' href='#L1436'><pre>1436</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1437' href='#L1437'><pre>1437</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1438' href='#L1438'><pre>1438</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn resources(&amp;self) -&gt; Option&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1439' href='#L1439'><pre>1439</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1440' href='#L1440'><pre>1440</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1441' href='#L1441'><pre>1441</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1442' href='#L1442'><pre>1442</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;&gt;&gt;(FBSResourceInfoList::VT_RESOURCES, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1443' href='#L1443'><pre>1443</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1444' href='#L1444'><pre>1444</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1445' href='#L1445'><pre>1445</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1446' href='#L1446'><pre>1446</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for FBSResourceInfoList&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1447' href='#L1447'><pre>1447</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1448' href='#L1448'><pre>1448</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L1449' href='#L1449'><pre>1449</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L1450' href='#L1450'><pre>1450</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L1451' href='#L1451'><pre>1451</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L1452' href='#L1452'><pre>1452</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1453' href='#L1453'><pre>1453</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;flatbuffers::Vector&lt;&apos;_, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;&gt;&gt;(&quot;resources&quot;, Self::VT_RESOURCES, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1454' href='#L1454'><pre>1454</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L1455' href='#L1455'><pre>1455</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L1456' href='#L1456'><pre>1456</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1457' href='#L1457'><pre>1457</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1458' href='#L1458'><pre>1458</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoListArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1459' href='#L1459'><pre>1459</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub resources: Option&lt;flatbuffers::WIPOffset&lt;flatbuffers::Vector&lt;&apos;a, flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;a&gt;&gt;&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1460' href='#L1460'><pre>1460</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1461' href='#L1461'><pre>1461</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for FBSResourceInfoListArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1462' href='#L1462'><pre>1462</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1463' href='#L1463'><pre>1463</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1464' href='#L1464'><pre>1464</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoListArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L1465' href='#L1465'><pre>1465</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      resources: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1466' href='#L1466'><pre>1466</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1467' href='#L1467'><pre>1467</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1468' href='#L1468'><pre>1468</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1469' href='#L1469'><pre>1469</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1470' href='#L1470'><pre>1470</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct FBSResourceInfoListBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1471' href='#L1471'><pre>1471</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1472' href='#L1472'><pre>1472</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1473' href='#L1473'><pre>1473</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1474' href='#L1474'><pre>1474</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; FBSResourceInfoListBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1475' href='#L1475'><pre>1475</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1476' href='#L1476'><pre>1476</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_resources(&amp;mut self, resources: flatbuffers::WIPOffset&lt;flatbuffers::Vector&lt;&apos;b , flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&lt;&apos;b &gt;&gt;&gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1477' href='#L1477'><pre>1477</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(FBSResourceInfoList::VT_RESOURCES, resources);</span></pre></td></tr><tr><td class='line-number'><a name='L1478' href='#L1478'><pre>1478</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1479' href='#L1479'><pre>1479</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1480' href='#L1480'><pre>1480</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; FBSResourceInfoListBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1481' href='#L1481'><pre>1481</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L1482' href='#L1482'><pre>1482</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    FBSResourceInfoListBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L1483' href='#L1483'><pre>1483</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L1484' href='#L1484'><pre>1484</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L1485' href='#L1485'><pre>1485</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1486' href='#L1486'><pre>1486</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1487' href='#L1487'><pre>1487</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1488' href='#L1488'><pre>1488</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;FBSResourceInfoList&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1489' href='#L1489'><pre>1489</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L1490' href='#L1490'><pre>1490</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L1491' href='#L1491'><pre>1491</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1492' href='#L1492'><pre>1492</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1493' href='#L1493'><pre>1493</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1494' href='#L1494'><pre>1494</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for FBSResourceInfoList&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1495' href='#L1495'><pre>1495</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L1496' href='#L1496'><pre>1496</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;FBSResourceInfoList&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L1497' href='#L1497'><pre>1497</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;resources&quot;, &amp;self.resources());</span></pre></td></tr><tr><td class='line-number'><a name='L1498' href='#L1498'><pre>1498</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1499' href='#L1499'><pre>1499</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1500' href='#L1500'><pre>1500</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1501' href='#L1501'><pre>1501</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub enum ResourceFlatOffset {}</pre></td></tr><tr><td class='line-number'><a name='L1502' href='#L1502'><pre>1502</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Copy, Clone, PartialEq)]</pre></td></tr><tr><td class='line-number'><a name='L1503' href='#L1503'><pre>1503</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1504' href='#L1504'><pre>1504</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceFlat&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1505' href='#L1505'><pre>1505</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub _tab: flatbuffers::Table&lt;&apos;a&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1506' href='#L1506'><pre>1506</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1507' href='#L1507'><pre>1507</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1508' href='#L1508'><pre>1508</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; flatbuffers::Follow&lt;&apos;a&gt; for ResourceFlat&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1509' href='#L1509'><pre>1509</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  type Inner = ResourceFlat&lt;&apos;a&gt;;</pre></td></tr><tr><td class='line-number'><a name='L1510' href='#L1510'><pre>1510</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1511' href='#L1511'><pre>1511</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>unsafe fn follow(buf: &amp;&apos;a [u8], loc: usize) -&gt; Self::Inner {</span></pre></td></tr><tr><td class='line-number'><a name='L1512' href='#L1512'><pre>1512</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Self { _tab: flatbuffers::Table::new(buf, loc) }</span></pre></td></tr><tr><td class='line-number'><a name='L1513' href='#L1513'><pre>1513</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1514' href='#L1514'><pre>1514</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1515' href='#L1515'><pre>1515</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1516' href='#L1516'><pre>1516</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; ResourceFlat&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1517' href='#L1517'><pre>1517</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_CONTAINER_TYPE: flatbuffers::VOffsetT = 4;</pre></td></tr><tr><td class='line-number'><a name='L1518' href='#L1518'><pre>1518</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_CONTAINER: flatbuffers::VOffsetT = 6;</pre></td></tr><tr><td class='line-number'><a name='L1519' href='#L1519'><pre>1519</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_CODE: flatbuffers::VOffsetT = 8;</pre></td></tr><tr><td class='line-number'><a name='L1520' href='#L1520'><pre>1520</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  pub const VT_ERROR: flatbuffers::VOffsetT = 10;</pre></td></tr><tr><td class='line-number'><a name='L1521' href='#L1521'><pre>1521</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1522' href='#L1522'><pre>1522</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1523' href='#L1523'><pre>1523</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub unsafe fn init_from_table(table: flatbuffers::Table&lt;&apos;a&gt;) -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1524' href='#L1524'><pre>1524</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ResourceFlat { _tab: table }</span></pre></td></tr><tr><td class='line-number'><a name='L1525' href='#L1525'><pre>1525</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1526' href='#L1526'><pre>1526</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(unused_mut)]</pre></td></tr><tr><td class='line-number'><a name='L1527' href='#L1527'><pre>1527</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn create&lt;&apos;bldr: &apos;args, &apos;args: &apos;mut_bldr, &apos;mut_bldr, A: flatbuffers::Allocator + &apos;bldr&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1528' href='#L1528'><pre>1528</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    _fbb: &amp;&apos;mut_bldr mut flatbuffers::FlatBufferBuilder&lt;&apos;bldr, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L1529' href='#L1529'><pre>1529</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    args: &amp;&apos;args ResourceFlatArgs&lt;&apos;args&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L1530' href='#L1530'><pre>1530</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; flatbuffers::WIPOffset&lt;ResourceFlat&lt;&apos;bldr&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1531' href='#L1531'><pre>1531</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut builder = ResourceFlatBuilder::new(_fbb)</span>;</pre></td></tr><tr><td class='line-number'><a name='L1532' href='#L1532'><pre>1532</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.error</span> <span class='region red'>{ builder.add_error(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1533' href='#L1533'><pre>1533</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.add_code(args.code)</span>;</pre></td></tr><tr><td class='line-number'><a name='L1534' href='#L1534'><pre>1534</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    if let Some(<span class='region red'>x</span>) = <span class='region red'>args.container</span> <span class='region red'>{ builder.add_container(x); }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L1535' href='#L1535'><pre>1535</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>builder.add_container_type(args.container_type);</span></pre></td></tr><tr><td class='line-number'><a name='L1536' href='#L1536'><pre>1536</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    builder.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1537' href='#L1537'><pre>1537</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1538' href='#L1538'><pre>1538</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1539' href='#L1539'><pre>1539</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1540' href='#L1540'><pre>1540</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1541' href='#L1541'><pre>1541</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_type(&amp;self) -&gt; ResourceContainer {</span></pre></td></tr><tr><td class='line-number'><a name='L1542' href='#L1542'><pre>1542</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1543' href='#L1543'><pre>1543</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1544' href='#L1544'><pre>1544</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1545' href='#L1545'><pre>1545</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;ResourceContainer&gt;(ResourceFlat::VT_CONTAINER_TYPE, Some(ResourceContainer::NONE)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1546' href='#L1546'><pre>1546</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1547' href='#L1547'><pre>1547</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1548' href='#L1548'><pre>1548</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container(&amp;self) -&gt; Option&lt;flatbuffers::Table&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1549' href='#L1549'><pre>1549</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1550' href='#L1550'><pre>1550</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1551' href='#L1551'><pre>1551</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1552' href='#L1552'><pre>1552</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;flatbuffers::Table&lt;&apos;a&gt;&gt;&gt;(ResourceFlat::VT_CONTAINER, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1553' href='#L1553'><pre>1553</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1554' href='#L1554'><pre>1554</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1555' href='#L1555'><pre>1555</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn code(&amp;self) -&gt; i32 {</span></pre></td></tr><tr><td class='line-number'><a name='L1556' href='#L1556'><pre>1556</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1557' href='#L1557'><pre>1557</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1558' href='#L1558'><pre>1558</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1559' href='#L1559'><pre>1559</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;i32&gt;(ResourceFlat::VT_CODE, Some(0)).unwrap()}</span></pre></td></tr><tr><td class='line-number'><a name='L1560' href='#L1560'><pre>1560</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1561' href='#L1561'><pre>1561</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1562' href='#L1562'><pre>1562</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn error(&amp;self) -&gt; Option&lt;&amp;&apos;a str&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1563' href='#L1563'><pre>1563</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1564' href='#L1564'><pre>1564</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // Created from valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1565' href='#L1565'><pre>1565</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    // which contains a valid value in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1566' href='#L1566'><pre>1566</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    unsafe { self._tab.get::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(ResourceFlat::VT_ERROR, None)}</span></pre></td></tr><tr><td class='line-number'><a name='L1567' href='#L1567'><pre>1567</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1568' href='#L1568'><pre>1568</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1569' href='#L1569'><pre>1569</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1570' href='#L1570'><pre>1570</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_none_wrapper(&amp;self) -&gt; Option&lt;NoneWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1571' href='#L1571'><pre>1571</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::NoneWrapper</span> {</pre></td></tr><tr><td class='line-number'><a name='L1572' href='#L1572'><pre>1572</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1573' href='#L1573'><pre>1573</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1574' href='#L1574'><pre>1574</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1575' href='#L1575'><pre>1575</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1576' href='#L1576'><pre>1576</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { NoneWrapper::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1577' href='#L1577'><pre>1577</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1578' href='#L1578'><pre>1578</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1579' href='#L1579'><pre>1579</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1580' href='#L1580'><pre>1580</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1581' href='#L1581'><pre>1581</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1582' href='#L1582'><pre>1582</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1583' href='#L1583'><pre>1583</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1584' href='#L1584'><pre>1584</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1585' href='#L1585'><pre>1585</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_str_wrapper(&amp;self) -&gt; Option&lt;StrWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1586' href='#L1586'><pre>1586</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::StrWrapper</span> {</pre></td></tr><tr><td class='line-number'><a name='L1587' href='#L1587'><pre>1587</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1588' href='#L1588'><pre>1588</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1589' href='#L1589'><pre>1589</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1590' href='#L1590'><pre>1590</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1591' href='#L1591'><pre>1591</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { StrWrapper::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1592' href='#L1592'><pre>1592</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1593' href='#L1593'><pre>1593</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1594' href='#L1594'><pre>1594</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1595' href='#L1595'><pre>1595</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1596' href='#L1596'><pre>1596</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1597' href='#L1597'><pre>1597</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1598' href='#L1598'><pre>1598</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1599' href='#L1599'><pre>1599</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1600' href='#L1600'><pre>1600</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_bool_wrapper(&amp;self) -&gt; Option&lt;BoolWrapper&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1601' href='#L1601'><pre>1601</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::BoolWrapper</span> {</pre></td></tr><tr><td class='line-number'><a name='L1602' href='#L1602'><pre>1602</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1603' href='#L1603'><pre>1603</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1604' href='#L1604'><pre>1604</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1605' href='#L1605'><pre>1605</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1606' href='#L1606'><pre>1606</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { BoolWrapper::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1607' href='#L1607'><pre>1607</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1608' href='#L1608'><pre>1608</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1609' href='#L1609'><pre>1609</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1610' href='#L1610'><pre>1610</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1611' href='#L1611'><pre>1611</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1612' href='#L1612'><pre>1612</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1613' href='#L1613'><pre>1613</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1614' href='#L1614'><pre>1614</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1615' href='#L1615'><pre>1615</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_fbsresource_info(&amp;self) -&gt; Option&lt;FBSResourceInfo&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1616' href='#L1616'><pre>1616</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::FBSResourceInfo</span> {</pre></td></tr><tr><td class='line-number'><a name='L1617' href='#L1617'><pre>1617</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1618' href='#L1618'><pre>1618</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1619' href='#L1619'><pre>1619</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1620' href='#L1620'><pre>1620</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1621' href='#L1621'><pre>1621</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { FBSResourceInfo::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1622' href='#L1622'><pre>1622</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1623' href='#L1623'><pre>1623</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1624' href='#L1624'><pre>1624</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1625' href='#L1625'><pre>1625</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1626' href='#L1626'><pre>1626</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1627' href='#L1627'><pre>1627</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1628' href='#L1628'><pre>1628</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1629' href='#L1629'><pre>1629</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1630' href='#L1630'><pre>1630</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_fbsresource_info_list(&amp;self) -&gt; Option&lt;FBSResourceInfoList&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1631' href='#L1631'><pre>1631</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::FBSResourceInfoList</span> {</pre></td></tr><tr><td class='line-number'><a name='L1632' href='#L1632'><pre>1632</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1633' href='#L1633'><pre>1633</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1634' href='#L1634'><pre>1634</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1635' href='#L1635'><pre>1635</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1636' href='#L1636'><pre>1636</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { FBSResourceInfoList::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1637' href='#L1637'><pre>1637</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1638' href='#L1638'><pre>1638</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1639' href='#L1639'><pre>1639</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1640' href='#L1640'><pre>1640</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1641' href='#L1641'><pre>1641</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1642' href='#L1642'><pre>1642</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1643' href='#L1643'><pre>1643</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1644' href='#L1644'><pre>1644</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1645' href='#L1645'><pre>1645</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_fbsresource_info_result_call_back(&amp;self) -&gt; Option&lt;FBSResourceInfoResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1646' href='#L1646'><pre>1646</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::FBSResourceInfoResultCallBack</span> {</pre></td></tr><tr><td class='line-number'><a name='L1647' href='#L1647'><pre>1647</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1648' href='#L1648'><pre>1648</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1649' href='#L1649'><pre>1649</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1650' href='#L1650'><pre>1650</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1651' href='#L1651'><pre>1651</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { FBSResourceInfoResultCallBack::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1652' href='#L1652'><pre>1652</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1653' href='#L1653'><pre>1653</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1654' href='#L1654'><pre>1654</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1655' href='#L1655'><pre>1655</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1656' href='#L1656'><pre>1656</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1657' href='#L1657'><pre>1657</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1658' href='#L1658'><pre>1658</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1659' href='#L1659'><pre>1659</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1660' href='#L1660'><pre>1660</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_fbsresource_info_list_result_call_back(&amp;self) -&gt; Option&lt;FBSResourceInfoListResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1661' href='#L1661'><pre>1661</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::FBSResourceInfoListResultCallBack</span> {</pre></td></tr><tr><td class='line-number'><a name='L1662' href='#L1662'><pre>1662</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1663' href='#L1663'><pre>1663</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1664' href='#L1664'><pre>1664</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1665' href='#L1665'><pre>1665</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1666' href='#L1666'><pre>1666</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { FBSResourceInfoListResultCallBack::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1667' href='#L1667'><pre>1667</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1668' href='#L1668'><pre>1668</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1669' href='#L1669'><pre>1669</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1670' href='#L1670'><pre>1670</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1671' href='#L1671'><pre>1671</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1672' href='#L1672'><pre>1672</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1673' href='#L1673'><pre>1673</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1674' href='#L1674'><pre>1674</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[allow(non_snake_case)]</pre></td></tr><tr><td class='line-number'><a name='L1675' href='#L1675'><pre>1675</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn container_as_fbsresource_info_on_progress_result_call_back(&amp;self) -&gt; Option&lt;FBSResourceInfoOnProgressResultCallBack&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1676' href='#L1676'><pre>1676</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    if self.container_type() == ResourceContainer::FBSResourceInfoOnProgressResultCallBack</span> {</pre></td></tr><tr><td class='line-number'><a name='L1677' href='#L1677'><pre>1677</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>self.container().map(</span>|t| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1678' href='#L1678'><pre>1678</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Safety:</span></pre></td></tr><tr><td class='line-number'><a name='L1679' href='#L1679'><pre>1679</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Created from a valid Table for this object</span></pre></td></tr><tr><td class='line-number'><a name='L1680' href='#L1680'><pre>1680</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       // Which contains a valid union in this slot</span></pre></td></tr><tr><td class='line-number'><a name='L1681' href='#L1681'><pre>1681</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>       unsafe { FBSResourceInfoOnProgressResultCallBack::init_from_table(t) }</span></pre></td></tr><tr><td class='line-number'><a name='L1682' href='#L1682'><pre>1682</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>     }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L1683' href='#L1683'><pre>1683</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    } else {</pre></td></tr><tr><td class='line-number'><a name='L1684' href='#L1684'><pre>1684</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>None</span></pre></td></tr><tr><td class='line-number'><a name='L1685' href='#L1685'><pre>1685</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L1686' href='#L1686'><pre>1686</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1687' href='#L1687'><pre>1687</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1688' href='#L1688'><pre>1688</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1689' href='#L1689'><pre>1689</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1690' href='#L1690'><pre>1690</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl flatbuffers::Verifiable for ResourceFlat&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1691' href='#L1691'><pre>1691</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1692' href='#L1692'><pre>1692</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn run_verifier(</span></pre></td></tr><tr><td class='line-number'><a name='L1693' href='#L1693'><pre>1693</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    v: &amp;mut flatbuffers::Verifier, pos: usize</span></pre></td></tr><tr><td class='line-number'><a name='L1694' href='#L1694'><pre>1694</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  ) -&gt; Result&lt;(), flatbuffers::InvalidFlatbuffer&gt; </span>{</pre></td></tr><tr><td class='line-number'><a name='L1695' href='#L1695'><pre>1695</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    use self::flatbuffers::Verifiable;</pre></td></tr><tr><td class='line-number'><a name='L1696' href='#L1696'><pre>1696</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>v.visit_table(pos)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1697' href='#L1697'><pre>1697</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_union::&lt;ResourceContainer, _&gt;(&quot;container_type&quot;, Self::VT_CONTAINER_TYPE, &quot;container&quot;, Self::VT_CONTAINER, false, </span>|key, v, pos| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L1698' href='#L1698'><pre>1698</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        match key</span> {</pre></td></tr><tr><td class='line-number'><a name='L1699' href='#L1699'><pre>1699</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::NoneWrapper =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;NoneWrapper&gt;&gt;(&quot;ResourceContainer::NoneWrapper&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1700' href='#L1700'><pre>1700</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::StrWrapper =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;StrWrapper&gt;&gt;(&quot;ResourceContainer::StrWrapper&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1701' href='#L1701'><pre>1701</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::BoolWrapper =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;BoolWrapper&gt;&gt;(&quot;ResourceContainer::BoolWrapper&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1702' href='#L1702'><pre>1702</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::FBSResourceInfo =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfo&gt;&gt;(&quot;ResourceContainer::FBSResourceInfo&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1703' href='#L1703'><pre>1703</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::FBSResourceInfoList =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfoList&gt;&gt;(&quot;ResourceContainer::FBSResourceInfoList&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1704' href='#L1704'><pre>1704</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::FBSResourceInfoResultCallBack =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfoResultCallBack&gt;&gt;(&quot;ResourceContainer::FBSResourceInfoResultCallBack&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1705' href='#L1705'><pre>1705</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::FBSResourceInfoListResultCallBack =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfoListResultCallBack&gt;&gt;(&quot;ResourceContainer::FBSResourceInfoListResultCallBack&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1706' href='#L1706'><pre>1706</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          ResourceContainer::FBSResourceInfoOnProgressResultCallBack =&gt; <span class='region red'>v.verify_union_variant::&lt;flatbuffers::ForwardsUOffset&lt;FBSResourceInfoOnProgressResultCallBack&gt;&gt;(&quot;ResourceContainer::FBSResourceInfoOnProgressResultCallBack&quot;, pos)</span>,</pre></td></tr><tr><td class='line-number'><a name='L1707' href='#L1707'><pre>1707</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          _ =&gt; <span class='region red'>Ok(())</span>,</pre></td></tr><tr><td class='line-number'><a name='L1708' href='#L1708'><pre>1708</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L1709' href='#L1709'><pre>1709</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     <span class='region red'>}</span><span class='region red'>)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1710' href='#L1710'><pre>1710</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;i32&gt;(&quot;code&quot;, Self::VT_CODE, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1711' href='#L1711'><pre>1711</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>visit_field::&lt;flatbuffers::ForwardsUOffset&lt;&amp;str&gt;&gt;(&quot;error&quot;, Self::VT_ERROR, false)</span><span class='region red'>?</span></pre></td></tr><tr><td class='line-number'><a name='L1712' href='#L1712'><pre>1712</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>     .<span class='region red'>finish();</span></pre></td></tr><tr><td class='line-number'><a name='L1713' href='#L1713'><pre>1713</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    Ok(())</span></pre></td></tr><tr><td class='line-number'><a name='L1714' href='#L1714'><pre>1714</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1715' href='#L1715'><pre>1715</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1716' href='#L1716'><pre>1716</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceFlatArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1717' href='#L1717'><pre>1717</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub container_type: ResourceContainer,</pre></td></tr><tr><td class='line-number'><a name='L1718' href='#L1718'><pre>1718</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub container: Option&lt;flatbuffers::WIPOffset&lt;flatbuffers::UnionWIPOffset&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1719' href='#L1719'><pre>1719</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub code: i32,</pre></td></tr><tr><td class='line-number'><a name='L1720' href='#L1720'><pre>1720</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub error: Option&lt;flatbuffers::WIPOffset&lt;&amp;&apos;a str&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1721' href='#L1721'><pre>1721</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1722' href='#L1722'><pre>1722</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a&gt; Default for ResourceFlatArgs&lt;&apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1723' href='#L1723'><pre>1723</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1724' href='#L1724'><pre>1724</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn default() -&gt; Self {</span></pre></td></tr><tr><td class='line-number'><a name='L1725' href='#L1725'><pre>1725</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ResourceFlatArgs {</span></pre></td></tr><tr><td class='line-number'><a name='L1726' href='#L1726'><pre>1726</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      container_type: ResourceContainer::NONE,</span></pre></td></tr><tr><td class='line-number'><a name='L1727' href='#L1727'><pre>1727</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      container: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1728' href='#L1728'><pre>1728</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      code: 0,</span></pre></td></tr><tr><td class='line-number'><a name='L1729' href='#L1729'><pre>1729</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      error: None,</span></pre></td></tr><tr><td class='line-number'><a name='L1730' href='#L1730'><pre>1730</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1731' href='#L1731'><pre>1731</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1732' href='#L1732'><pre>1732</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1733' href='#L1733'><pre>1733</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1734' href='#L1734'><pre>1734</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ResourceFlatBuilder&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1735' href='#L1735'><pre>1735</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  fbb_: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1736' href='#L1736'><pre>1736</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  start_: flatbuffers::WIPOffset&lt;flatbuffers::TableUnfinishedWIPOffset&gt;,</pre></td></tr><tr><td class='line-number'><a name='L1737' href='#L1737'><pre>1737</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1738' href='#L1738'><pre>1738</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;a: &apos;b, &apos;b, A: flatbuffers::Allocator + &apos;a&gt; ResourceFlatBuilder&lt;&apos;a, &apos;b, A&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1739' href='#L1739'><pre>1739</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1740' href='#L1740'><pre>1740</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_container_type(&amp;mut self, container_type: ResourceContainer) {</span></pre></td></tr><tr><td class='line-number'><a name='L1741' href='#L1741'><pre>1741</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;ResourceContainer&gt;(ResourceFlat::VT_CONTAINER_TYPE, container_type, ResourceContainer::NONE);</span></pre></td></tr><tr><td class='line-number'><a name='L1742' href='#L1742'><pre>1742</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1743' href='#L1743'><pre>1743</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1744' href='#L1744'><pre>1744</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_container(&amp;mut self, container: flatbuffers::WIPOffset&lt;flatbuffers::UnionWIPOffset&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1745' href='#L1745'><pre>1745</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(ResourceFlat::VT_CONTAINER, container);</span></pre></td></tr><tr><td class='line-number'><a name='L1746' href='#L1746'><pre>1746</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1747' href='#L1747'><pre>1747</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1748' href='#L1748'><pre>1748</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_code(&amp;mut self, code: i32) {</span></pre></td></tr><tr><td class='line-number'><a name='L1749' href='#L1749'><pre>1749</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot::&lt;i32&gt;(ResourceFlat::VT_CODE, code, 0);</span></pre></td></tr><tr><td class='line-number'><a name='L1750' href='#L1750'><pre>1750</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1751' href='#L1751'><pre>1751</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1752' href='#L1752'><pre>1752</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn add_error(&amp;mut self, error: flatbuffers::WIPOffset&lt;&amp;&apos;b  str&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1753' href='#L1753'><pre>1753</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    self.fbb_.push_slot_always::&lt;flatbuffers::WIPOffset&lt;_&gt;&gt;(ResourceFlat::VT_ERROR, error);</span></pre></td></tr><tr><td class='line-number'><a name='L1754' href='#L1754'><pre>1754</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1755' href='#L1755'><pre>1755</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1756' href='#L1756'><pre>1756</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn new(_fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;) -&gt; ResourceFlatBuilder&lt;&apos;a, &apos;b, A&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1757' href='#L1757'><pre>1757</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let start = _fbb.start_table();</span></pre></td></tr><tr><td class='line-number'><a name='L1758' href='#L1758'><pre>1758</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ResourceFlatBuilder {</span></pre></td></tr><tr><td class='line-number'><a name='L1759' href='#L1759'><pre>1759</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      fbb_: _fbb,</span></pre></td></tr><tr><td class='line-number'><a name='L1760' href='#L1760'><pre>1760</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      start_: start,</span></pre></td></tr><tr><td class='line-number'><a name='L1761' href='#L1761'><pre>1761</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L1762' href='#L1762'><pre>1762</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1763' href='#L1763'><pre>1763</pre></a></td><td class='skipped-line'></td><td class='code'><pre>  #[inline]</pre></td></tr><tr><td class='line-number'><a name='L1764' href='#L1764'><pre>1764</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>pub fn finish(self) -&gt; flatbuffers::WIPOffset&lt;ResourceFlat&lt;&apos;a&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1765' href='#L1765'><pre>1765</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let o = self.fbb_.end_table(self.start_);</span></pre></td></tr><tr><td class='line-number'><a name='L1766' href='#L1766'><pre>1766</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    flatbuffers::WIPOffset::new(o.value())</span></pre></td></tr><tr><td class='line-number'><a name='L1767' href='#L1767'><pre>1767</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1768' href='#L1768'><pre>1768</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1769' href='#L1769'><pre>1769</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1770' href='#L1770'><pre>1770</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl core::fmt::Debug for ResourceFlat&lt;&apos;_&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1771' href='#L1771'><pre>1771</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>  <span class='region red'>fn fmt(&amp;self, f: &amp;mut core::fmt::Formatter&lt;&apos;_&gt;) -&gt; core::fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L1772' href='#L1772'><pre>1772</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    let mut ds = f.debug_struct(&quot;ResourceFlat&quot;);</span></pre></td></tr><tr><td class='line-number'><a name='L1773' href='#L1773'><pre>1773</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;container_type&quot;, &amp;self.container_type());</span></pre></td></tr><tr><td class='line-number'><a name='L1774' href='#L1774'><pre>1774</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      match self.container_type()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1775' href='#L1775'><pre>1775</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::NoneWrapper =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1776' href='#L1776'><pre>1776</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_none_wrapper()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1777' href='#L1777'><pre>1777</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1778' href='#L1778'><pre>1778</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1779' href='#L1779'><pre>1779</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1780' href='#L1780'><pre>1780</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1781' href='#L1781'><pre>1781</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1782' href='#L1782'><pre>1782</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::StrWrapper =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1783' href='#L1783'><pre>1783</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_str_wrapper()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1784' href='#L1784'><pre>1784</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1785' href='#L1785'><pre>1785</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1786' href='#L1786'><pre>1786</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1787' href='#L1787'><pre>1787</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1788' href='#L1788'><pre>1788</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1789' href='#L1789'><pre>1789</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::BoolWrapper =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1790' href='#L1790'><pre>1790</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_bool_wrapper()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1791' href='#L1791'><pre>1791</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1792' href='#L1792'><pre>1792</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1793' href='#L1793'><pre>1793</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1794' href='#L1794'><pre>1794</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1795' href='#L1795'><pre>1795</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1796' href='#L1796'><pre>1796</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::FBSResourceInfo =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1797' href='#L1797'><pre>1797</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_fbsresource_info()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1798' href='#L1798'><pre>1798</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1799' href='#L1799'><pre>1799</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1800' href='#L1800'><pre>1800</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1801' href='#L1801'><pre>1801</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1802' href='#L1802'><pre>1802</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1803' href='#L1803'><pre>1803</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::FBSResourceInfoList =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1804' href='#L1804'><pre>1804</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_fbsresource_info_list()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1805' href='#L1805'><pre>1805</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1806' href='#L1806'><pre>1806</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1807' href='#L1807'><pre>1807</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1808' href='#L1808'><pre>1808</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1809' href='#L1809'><pre>1809</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1810' href='#L1810'><pre>1810</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::FBSResourceInfoResultCallBack =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1811' href='#L1811'><pre>1811</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_fbsresource_info_result_call_back()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1812' href='#L1812'><pre>1812</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1813' href='#L1813'><pre>1813</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1814' href='#L1814'><pre>1814</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1815' href='#L1815'><pre>1815</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1816' href='#L1816'><pre>1816</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1817' href='#L1817'><pre>1817</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::FBSResourceInfoListResultCallBack =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1818' href='#L1818'><pre>1818</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_fbsresource_info_list_result_call_back()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1819' href='#L1819'><pre>1819</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1820' href='#L1820'><pre>1820</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1821' href='#L1821'><pre>1821</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1822' href='#L1822'><pre>1822</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1823' href='#L1823'><pre>1823</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1824' href='#L1824'><pre>1824</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        ResourceContainer::FBSResourceInfoOnProgressResultCallBack =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1825' href='#L1825'><pre>1825</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          if let Some(<span class='region red'>x</span>) = <span class='region red'>self.container_as_fbsresource_info_on_progress_result_call_back()</span> {</pre></td></tr><tr><td class='line-number'><a name='L1826' href='#L1826'><pre>1826</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1827' href='#L1827'><pre>1827</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          } else {</pre></td></tr><tr><td class='line-number'><a name='L1828' href='#L1828'><pre>1828</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            <span class='region red'>ds.field(&quot;container&quot;, &amp;&quot;InvalidFlatbuffer: Union discriminant does not match value.&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L1829' href='#L1829'><pre>1829</pre></a></td><td class='skipped-line'></td><td class='code'><pre>          }</pre></td></tr><tr><td class='line-number'><a name='L1830' href='#L1830'><pre>1830</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1831' href='#L1831'><pre>1831</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L1832' href='#L1832'><pre>1832</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>          let <span class='region red'>x: Option&lt;()&gt; = None;</span></pre></td></tr><tr><td class='line-number'><a name='L1833' href='#L1833'><pre>1833</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>          ds.field(&quot;container&quot;, &amp;x)</span></pre></td></tr><tr><td class='line-number'><a name='L1834' href='#L1834'><pre>1834</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        },</pre></td></tr><tr><td class='line-number'><a name='L1835' href='#L1835'><pre>1835</pre></a></td><td class='skipped-line'></td><td class='code'><pre>      };</pre></td></tr><tr><td class='line-number'><a name='L1836' href='#L1836'><pre>1836</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>      <span class='region red'>ds.field(&quot;code&quot;, &amp;self.code());</span></pre></td></tr><tr><td class='line-number'><a name='L1837' href='#L1837'><pre>1837</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.field(&quot;error&quot;, &amp;self.error());</span></pre></td></tr><tr><td class='line-number'><a name='L1838' href='#L1838'><pre>1838</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>      ds.finish()</span></pre></td></tr><tr><td class='line-number'><a name='L1839' href='#L1839'><pre>1839</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  }</span></pre></td></tr><tr><td class='line-number'><a name='L1840' href='#L1840'><pre>1840</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L1841' href='#L1841'><pre>1841</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1842' href='#L1842'><pre>1842</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Verifies that a buffer of bytes contains a `ResourceFlat`</pre></td></tr><tr><td class='line-number'><a name='L1843' href='#L1843'><pre>1843</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// and returns it.</pre></td></tr><tr><td class='line-number'><a name='L1844' href='#L1844'><pre>1844</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Note that verification is still experimental and may not</pre></td></tr><tr><td class='line-number'><a name='L1845' href='#L1845'><pre>1845</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// catch every error, or be maximally performant. For the</pre></td></tr><tr><td class='line-number'><a name='L1846' href='#L1846'><pre>1846</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// previous, unchecked, behavior use</pre></td></tr><tr><td class='line-number'><a name='L1847' href='#L1847'><pre>1847</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// `root_as_resource_flat_unchecked`.</pre></td></tr><tr><td class='line-number'><a name='L1848' href='#L1848'><pre>1848</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn root_as_resource_flat(buf: &amp;[u8]) -&gt; Result&lt;ResourceFlat, flatbuffers::InvalidFlatbuffer&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1849' href='#L1849'><pre>1849</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::root::&lt;ResourceFlat&gt;(buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1850' href='#L1850'><pre>1850</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1851' href='#L1851'><pre>1851</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1852' href='#L1852'><pre>1852</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Verifies that a buffer of bytes contains a size prefixed</pre></td></tr><tr><td class='line-number'><a name='L1853' href='#L1853'><pre>1853</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// `ResourceFlat` and returns it.</pre></td></tr><tr><td class='line-number'><a name='L1854' href='#L1854'><pre>1854</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Note that verification is still experimental and may not</pre></td></tr><tr><td class='line-number'><a name='L1855' href='#L1855'><pre>1855</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// catch every error, or be maximally performant. For the</pre></td></tr><tr><td class='line-number'><a name='L1856' href='#L1856'><pre>1856</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// previous, unchecked, behavior use</pre></td></tr><tr><td class='line-number'><a name='L1857' href='#L1857'><pre>1857</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// `size_prefixed_root_as_resource_flat_unchecked`.</pre></td></tr><tr><td class='line-number'><a name='L1858' href='#L1858'><pre>1858</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn size_prefixed_root_as_resource_flat(buf: &amp;[u8]) -&gt; Result&lt;ResourceFlat, flatbuffers::InvalidFlatbuffer&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1859' href='#L1859'><pre>1859</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::size_prefixed_root::&lt;ResourceFlat&gt;(buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1860' href='#L1860'><pre>1860</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1861' href='#L1861'><pre>1861</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1862' href='#L1862'><pre>1862</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Verifies, with the given options, that a buffer of bytes</pre></td></tr><tr><td class='line-number'><a name='L1863' href='#L1863'><pre>1863</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// contains a `ResourceFlat` and returns it.</pre></td></tr><tr><td class='line-number'><a name='L1864' href='#L1864'><pre>1864</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Note that verification is still experimental and may not</pre></td></tr><tr><td class='line-number'><a name='L1865' href='#L1865'><pre>1865</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// catch every error, or be maximally performant. For the</pre></td></tr><tr><td class='line-number'><a name='L1866' href='#L1866'><pre>1866</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// previous, unchecked, behavior use</pre></td></tr><tr><td class='line-number'><a name='L1867' href='#L1867'><pre>1867</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// `root_as_resource_flat_unchecked`.</pre></td></tr><tr><td class='line-number'><a name='L1868' href='#L1868'><pre>1868</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn root_as_resource_flat_with_opts&lt;&apos;b, &apos;o&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1869' href='#L1869'><pre>1869</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  opts: &amp;&apos;o flatbuffers::VerifierOptions,</span></pre></td></tr><tr><td class='line-number'><a name='L1870' href='#L1870'><pre>1870</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  buf: &amp;&apos;b [u8],</span></pre></td></tr><tr><td class='line-number'><a name='L1871' href='#L1871'><pre>1871</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Result&lt;ResourceFlat&lt;&apos;b&gt;, flatbuffers::InvalidFlatbuffer&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1872' href='#L1872'><pre>1872</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::root_with_opts::&lt;ResourceFlat&lt;&apos;b&gt;&gt;(opts, buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1873' href='#L1873'><pre>1873</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1874' href='#L1874'><pre>1874</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1875' href='#L1875'><pre>1875</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Verifies, with the given verifier options, that a buffer of</pre></td></tr><tr><td class='line-number'><a name='L1876' href='#L1876'><pre>1876</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// bytes contains a size prefixed `ResourceFlat` and returns</pre></td></tr><tr><td class='line-number'><a name='L1877' href='#L1877'><pre>1877</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// it. Note that verification is still experimental and may not</pre></td></tr><tr><td class='line-number'><a name='L1878' href='#L1878'><pre>1878</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// catch every error, or be maximally performant. For the</pre></td></tr><tr><td class='line-number'><a name='L1879' href='#L1879'><pre>1879</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// previous, unchecked, behavior use</pre></td></tr><tr><td class='line-number'><a name='L1880' href='#L1880'><pre>1880</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// `root_as_resource_flat_unchecked`.</pre></td></tr><tr><td class='line-number'><a name='L1881' href='#L1881'><pre>1881</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn size_prefixed_root_as_resource_flat_with_opts&lt;&apos;b, &apos;o&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1882' href='#L1882'><pre>1882</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  opts: &amp;&apos;o flatbuffers::VerifierOptions,</span></pre></td></tr><tr><td class='line-number'><a name='L1883' href='#L1883'><pre>1883</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  buf: &amp;&apos;b [u8],</span></pre></td></tr><tr><td class='line-number'><a name='L1884' href='#L1884'><pre>1884</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>) -&gt; Result&lt;ResourceFlat&lt;&apos;b&gt;, flatbuffers::InvalidFlatbuffer&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L1885' href='#L1885'><pre>1885</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::size_prefixed_root_with_opts::&lt;ResourceFlat&lt;&apos;b&gt;&gt;(opts, buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1886' href='#L1886'><pre>1886</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1887' href='#L1887'><pre>1887</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1888' href='#L1888'><pre>1888</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Assumes, without verification, that a buffer of bytes contains a ResourceFlat and returns it.</pre></td></tr><tr><td class='line-number'><a name='L1889' href='#L1889'><pre>1889</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// # Safety</pre></td></tr><tr><td class='line-number'><a name='L1890' href='#L1890'><pre>1890</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Callers must trust the given bytes do indeed contain a valid `ResourceFlat`.</pre></td></tr><tr><td class='line-number'><a name='L1891' href='#L1891'><pre>1891</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub unsafe fn root_as_resource_flat_unchecked(buf: &amp;[u8]) -&gt; ResourceFlat {</span></pre></td></tr><tr><td class='line-number'><a name='L1892' href='#L1892'><pre>1892</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::root_unchecked::&lt;ResourceFlat&gt;(buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1893' href='#L1893'><pre>1893</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1894' href='#L1894'><pre>1894</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1895' href='#L1895'><pre>1895</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Assumes, without verification, that a buffer of bytes contains a size prefixed ResourceFlat and returns it.</pre></td></tr><tr><td class='line-number'><a name='L1896' href='#L1896'><pre>1896</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// # Safety</pre></td></tr><tr><td class='line-number'><a name='L1897' href='#L1897'><pre>1897</pre></a></td><td class='skipped-line'></td><td class='code'><pre>/// Callers must trust the given bytes do indeed contain a valid size prefixed `ResourceFlat`.</pre></td></tr><tr><td class='line-number'><a name='L1898' href='#L1898'><pre>1898</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub unsafe fn size_prefixed_root_as_resource_flat_unchecked(buf: &amp;[u8]) -&gt; ResourceFlat {</span></pre></td></tr><tr><td class='line-number'><a name='L1899' href='#L1899'><pre>1899</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  flatbuffers::size_prefixed_root_unchecked::&lt;ResourceFlat&gt;(buf)</span></pre></td></tr><tr><td class='line-number'><a name='L1900' href='#L1900'><pre>1900</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1901' href='#L1901'><pre>1901</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1902' href='#L1902'><pre>1902</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn finish_resource_flat_buffer&lt;&apos;a, &apos;b, A: flatbuffers::Allocator + &apos;a&gt;(</span></pre></td></tr><tr><td class='line-number'><a name='L1903' href='#L1903'><pre>1903</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L1904' href='#L1904'><pre>1904</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    root: flatbuffers::WIPOffset&lt;ResourceFlat&lt;&apos;a&gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1905' href='#L1905'><pre>1905</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  fbb.finish(root, None);</span></pre></td></tr><tr><td class='line-number'><a name='L1906' href='#L1906'><pre>1906</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1907' href='#L1907'><pre>1907</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L1908' href='#L1908'><pre>1908</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[inline]</pre></td></tr><tr><td class='line-number'><a name='L1909' href='#L1909'><pre>1909</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>pub fn finish_size_prefixed_resource_flat_buffer&lt;&apos;a, &apos;b, A: flatbuffers::Allocator + &apos;a&gt;(fbb: &amp;&apos;b mut flatbuffers::FlatBufferBuilder&lt;&apos;a, A&gt;, root: flatbuffers::WIPOffset&lt;ResourceFlat&lt;&apos;a&gt;&gt;) {</span></pre></td></tr><tr><td class='line-number'><a name='L1910' href='#L1910'><pre>1910</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>  fbb.finish_size_prefixed(root, None);</span></pre></td></tr><tr><td class='line-number'><a name='L1911' href='#L1911'><pre>1911</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L1912' href='#L1912'><pre>1912</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod fbs</pre></td></tr><tr><td class='line-number'><a name='L1913' href='#L1913'><pre>1913</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod resource</pre></td></tr><tr><td class='line-number'><a name='L1914' href='#L1914'><pre>1914</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod rust</pre></td></tr><tr><td class='line-number'><a name='L1915' href='#L1915'><pre>1915</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod uplus</pre></td></tr><tr><td class='line-number'><a name='L1916' href='#L1916'><pre>1916</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod uhome</pre></td></tr><tr><td class='line-number'><a name='L1917' href='#L1917'><pre>1917</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod haier</pre></td></tr><tr><td class='line-number'><a name='L1918' href='#L1918'><pre>1918</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}  // pub mod com</pre></td></tr><tr><td class='line-number'><a name='L1919' href='#L1919'><pre>1919</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr></table></div></body></html>