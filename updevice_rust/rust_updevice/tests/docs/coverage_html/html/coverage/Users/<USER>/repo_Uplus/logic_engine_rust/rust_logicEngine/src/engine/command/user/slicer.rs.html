<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../../../style.css'><script src='../../../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/logic_engine_rust/rust_logicEngine/src/engine/command/user/slicer.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::collections::HashMap;</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::sync::Arc;</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use parking_lot::RwLock;</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device::command::Command;</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device::device_command::UpDeviceCommand;</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::attribute::LEAttribute;</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::command::factory::{CommandPrepareFactory, LECommandPrepareFactory};</pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::command::LEGroupCommand;</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::command::preparer::{DeviceCommandPreparer, UserCommandPreparer};</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::error::LogicEngineError::CommandPrepareError;</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::error::Result;</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::engine::splitter::LESplitter;</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct UserCommandSlicer {</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    next: RwLock&lt;Option&lt;Arc&lt;dyn UserCommandPreparer&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    attribute_map: HashMap&lt;String, Arc&lt;LEAttribute&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    device_command_preparer: Box&lt;dyn DeviceCommandPreparer&gt;,</pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl UserCommandSlicer {</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    pub fn new(</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        attribute_map: HashMap&lt;String, Arc&lt;LEAttribute&gt;&gt;, </pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        group_commands: Vec&lt;LEGroupCommand&gt;,</pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        splitter: Vec&lt;LESplitter&gt;, </pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    ) -&gt; Self {</pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        let device_command_preparer = LECommandPrepareFactory::create_device_command_preparer(</pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            group_commands,</pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            splitter,</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        );</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        </pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        UserCommandSlicer {</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            next: RwLock::new(None),</pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            attribute_map,</pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>            device_command_preparer,</pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='covered-line'><pre>48</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl UserCommandPreparer for UserCommandSlicer {</pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn lock(&amp;self) -&gt; &amp;RwLock&lt;Option&lt;Arc&lt;dyn UserCommandPreparer&gt;&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self.next</span></pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    </pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn prepare(</span></pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        &amp;self, </span></pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        user_commands: &amp;[Command], </span></pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        _device_command: Option&lt;UpDeviceCommand&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    ) -&gt; Result&lt;Option&lt;Vec&lt;UpDeviceCommand&gt;&gt;&gt; {</span></pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        debug_assert!</span>(<span class='region red'>_device_command.is_some()</span>);</pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        </pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        let <span class='region red'>mut user_command_cache: HashMap&lt;String, String&gt; = HashMap::new();</span></pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        user_commands.iter()</span></pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .filter(</span>|command| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                command.is_manual()</span></pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            .for_each(</span>|command| <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                user_command_cache.insert(command.name().to_string(), command.value().to_string());</span></pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            }</span><span class='region red'>);</span></pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        </span></pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut device_commands: Vec&lt;UpDeviceCommand&gt; = vec![];</span></pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut mut_user_commands: Vec&lt;Command&gt; = user_commands.to_vec()</span>;</pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        while <span class='region red'>!mut_user_commands.is_empty()</span> {</pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            let <span class='region red'>device_command</span> = <span class='region red'>self.device_command_preparer.prepare(</span></pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                None,</span></pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;mut mut_user_commands,</span></pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;user_command_cache,</span></pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &amp;self.attribute_map</span></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            )</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            </pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            if let Some(<span class='region red'>device_command</span>) = <span class='region red'>device_command</span> {</pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>               <span class='region red'>device_commands.push(device_command)</span> </pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            } else { </pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                return <span class='region red'>Err(CommandPrepareError(&quot;can not slice a device command from user commands&quot;.to_string()))</span></pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        </pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        <span class='region red'>Ok(Some(device_commands))</span></pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr></table></div></body></html>