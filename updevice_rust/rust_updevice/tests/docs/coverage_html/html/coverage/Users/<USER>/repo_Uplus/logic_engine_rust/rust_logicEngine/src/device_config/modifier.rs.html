<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='../../../../../../../../style.css'><script src='../../../../../../../../control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-06-06 18:39</h4><span class='control'><a href='javascript:next_line()'>next uncovered line (L)</a>, <a href='javascript:next_region()'>next uncovered region (R)</a>, <a href='javascript:next_branch()'>next uncovered branch (B)</a></span><div class='centered'><table><div class='source-name-title'><pre>/Users/<USER>/repo_Uplus/logic_engine_rust/rust_logicEngine/src/device_config/modifier.rs</pre></div><tr><td><pre>Line</pre></td><td><pre>Count</pre></td><td><pre>Source</pre></td></tr><tr><td class='line-number'><a name='L1' href='#L1'><pre>1</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use super::value_range::ValueRange;</pre></td></tr><tr><td class='line-number'><a name='L2' href='#L2'><pre>2</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use crate::device_config::deserialize_rules::Validator;</pre></td></tr><tr><td class='line-number'><a name='L3' href='#L3'><pre>3</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::de::{MapAccess, Visitor};</pre></td></tr><tr><td class='line-number'><a name='L4' href='#L4'><pre>4</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use serde::{Deserialize, Deserializer, Serialize};</pre></td></tr><tr><td class='line-number'><a name='L5' href='#L5'><pre>5</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::collections::HashMap;</pre></td></tr><tr><td class='line-number'><a name='L6' href='#L6'><pre>6</pre></a></td><td class='skipped-line'></td><td class='code'><pre>use std::fmt;</pre></td></tr><tr><td class='line-number'><a name='L7' href='#L7'><pre>7</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L8' href='#L8'><pre>8</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Clone)]</pre></td></tr><tr><td class='line-number'><a name='L9' href='#L9'><pre>9</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L10' href='#L10'><pre>10</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct Modifier {</pre></td></tr><tr><td class='line-number'><a name='L11' href='#L11'><pre>11</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub priority: Option&lt;i32&gt;,</pre></td></tr><tr><td class='line-number'><a name='L12' href='#L12'><pre>12</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub trigger: ModifierTrigger,</pre></td></tr><tr><td class='line-number'><a name='L13' href='#L13'><pre>13</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub actions: Vec&lt;ModifierAction&gt;,</pre></td></tr><tr><td class='line-number'><a name='L14' href='#L14'><pre>14</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L15' href='#L15'><pre>15</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L16' href='#L16'><pre>16</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for Modifier {</pre></td></tr><tr><td class='line-number'><a name='L17' href='#L17'><pre>17</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L18' href='#L18'><pre>18</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        self.trigger.is_valid()</span> &amp;&amp; <span class='region red'>!self.actions.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L19' href='#L19'><pre>19</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L20' href='#L20'><pre>20</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L21' href='#L21'><pre>21</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for Modifier {</pre></td></tr><tr><td class='line-number'><a name='L22' href='#L22'><pre>22</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L23' href='#L23'><pre>23</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L24' href='#L24'><pre>24</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L25' href='#L25'><pre>25</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    {</span></pre></td></tr><tr><td class='line-number'><a name='L26' href='#L26'><pre>26</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        deserializer.deserialize_map(ModifierVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L27' href='#L27'><pre>27</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L28' href='#L28'><pre>28</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L29' href='#L29'><pre>29</pre></a></td><td class='skipped-line'></td><td class='code'><pre>struct ModifierVisitor;</pre></td></tr><tr><td class='line-number'><a name='L30' href='#L30'><pre>30</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for ModifierVisitor {</pre></td></tr><tr><td class='line-number'><a name='L31' href='#L31'><pre>31</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Value = Modifier;</pre></td></tr><tr><td class='line-number'><a name='L32' href='#L32'><pre>32</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L33' href='#L33'><pre>33</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L34' href='#L34'><pre>34</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        formatter.write_str(&quot;a struct of CmdPara&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L35' href='#L35'><pre>35</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L36' href='#L36'><pre>36</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L37' href='#L37'><pre>37</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L38' href='#L38'><pre>38</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L39' href='#L39'><pre>39</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        A: MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L40' href='#L40'><pre>40</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    {</span></pre></td></tr><tr><td class='line-number'><a name='L41' href='#L41'><pre>41</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut priority = None;</span></pre></td></tr><tr><td class='line-number'><a name='L42' href='#L42'><pre>42</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut trigger = None;</span></pre></td></tr><tr><td class='line-number'><a name='L43' href='#L43'><pre>43</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut actions = None</span>;</pre></td></tr><tr><td class='line-number'><a name='L44' href='#L44'><pre>44</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L45' href='#L45'><pre>45</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L46' href='#L46'><pre>46</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L47' href='#L47'><pre>47</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;priority&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L48' href='#L48'><pre>48</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    priority = match <span class='region red'>map.next_value()</span> {</pre></td></tr><tr><td class='line-number'><a name='L49' href='#L49'><pre>49</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Ok(<span class='region red'>value) =&gt; Some(value)</span>,</pre></td></tr><tr><td class='line-number'><a name='L50' href='#L50'><pre>50</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Err(_) =&gt; <span class='region red'>Some(0)</span>,</pre></td></tr><tr><td class='line-number'><a name='L51' href='#L51'><pre>51</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L52' href='#L52'><pre>52</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L53' href='#L53'><pre>53</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>&quot;trigger&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L54' href='#L54'><pre>54</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>trigger</span> = match <span class='region red'>map.next_value()</span> {</pre></td></tr><tr><td class='line-number'><a name='L55' href='#L55'><pre>55</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Ok(<span class='region red'>value) =&gt; Some(value)</span>,</pre></td></tr><tr><td class='line-number'><a name='L56' href='#L56'><pre>56</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Err(_) =&gt; <span class='region red'>Some(ModifierTrigger::default())</span>,</pre></td></tr><tr><td class='line-number'><a name='L57' href='#L57'><pre>57</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L58' href='#L58'><pre>58</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L59' href='#L59'><pre>59</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>&quot;actions&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L60' href='#L60'><pre>60</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    let <span class='region red'>actions_list: Option&lt;Vec&lt;ModifierAction&gt;&gt; =</span></pre></td></tr><tr><td class='line-number'><a name='L61' href='#L61'><pre>61</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        map.next_value().unwrap_or(None)</span>;</pre></td></tr><tr><td class='line-number'><a name='L62' href='#L62'><pre>62</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    if let Some(<span class='region red'>actions_list</span>) = <span class='region red'>actions_list</span> <span class='region red'>{</span></pre></td></tr><tr><td class='line-number'><a name='L63' href='#L63'><pre>63</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        let filtered_actions = actions_list</span></pre></td></tr><tr><td class='line-number'><a name='L64' href='#L64'><pre>64</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            .into_iter()</span></pre></td></tr><tr><td class='line-number'><a name='L65' href='#L65'><pre>65</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            .filter(ModifierAction::is_valid)</span></pre></td></tr><tr><td class='line-number'><a name='L66' href='#L66'><pre>66</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                            .collect();</span></pre></td></tr><tr><td class='line-number'><a name='L67' href='#L67'><pre>67</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                        actions = Some(filtered_actions);</span></pre></td></tr><tr><td class='line-number'><a name='L68' href='#L68'><pre>68</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                    }</span><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L69' href='#L69'><pre>69</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L70' href='#L70'><pre>70</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L71' href='#L71'><pre>71</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    let _ = <span class='region red'>map.next_value::&lt;serde::de::IgnoredAny&gt;()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L72' href='#L72'><pre>72</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L73' href='#L73'><pre>73</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L74' href='#L74'><pre>74</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L75' href='#L75'><pre>75</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L76' href='#L76'><pre>76</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        let <span class='region red'>priority = priority.unwrap_or(0);</span></pre></td></tr><tr><td class='line-number'><a name='L77' href='#L77'><pre>77</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let trigger = trigger.unwrap_or_else(ModifierTrigger::default);</span></pre></td></tr><tr><td class='line-number'><a name='L78' href='#L78'><pre>78</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let actions = actions.unwrap_or_else(Vec::new);</span></pre></td></tr><tr><td class='line-number'><a name='L79' href='#L79'><pre>79</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L80' href='#L80'><pre>80</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Ok(Modifier {</span></pre></td></tr><tr><td class='line-number'><a name='L81' href='#L81'><pre>81</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            priority: Some(priority),</span></pre></td></tr><tr><td class='line-number'><a name='L82' href='#L82'><pre>82</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            trigger,</span></pre></td></tr><tr><td class='line-number'><a name='L83' href='#L83'><pre>83</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            actions,</span></pre></td></tr><tr><td class='line-number'><a name='L84' href='#L84'><pre>84</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        })</span></pre></td></tr><tr><td class='line-number'><a name='L85' href='#L85'><pre>85</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L86' href='#L86'><pre>86</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L87' href='#L87'><pre>87</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L88' href='#L88'><pre>88</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Default, Clone)]</pre></td></tr><tr><td class='line-number'><a name='L89' href='#L89'><pre>89</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ModifierTrigger {</pre></td></tr><tr><td class='line-number'><a name='L90' href='#L90'><pre>90</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub operator: String,</pre></td></tr><tr><td class='line-number'><a name='L91' href='#L91'><pre>91</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub conditions: Option&lt;HashMap&lt;String, Vec&lt;String&gt;&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L92' href='#L92'><pre>92</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub alarms: Option&lt;Vec&lt;String&gt;&gt;,</pre></td></tr><tr><td class='line-number'><a name='L93' href='#L93'><pre>93</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L94' href='#L94'><pre>94</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for ModifierTrigger {</pre></td></tr><tr><td class='line-number'><a name='L95' href='#L95'><pre>95</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L96' href='#L96'><pre>96</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !self.operator.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L97' href='#L97'><pre>97</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; (<span class='region red'>self.operator.eq(&quot;AND&quot;)</span> || <span class='region red'>self.operator.eq(&quot;OR&quot;)</span>)</pre></td></tr><tr><td class='line-number'><a name='L98' href='#L98'><pre>98</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; (<span class='region red'>self.conditions.as_ref().map_or(false, </span>|c| <span class='region red'>!c.is_empty()</span><span class='region red'>)</span></pre></td></tr><tr><td class='line-number'><a name='L99' href='#L99'><pre>99</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                || <span class='region red'>self.alarms.as_ref().map_or(false, </span>|a| <span class='region red'>!a.is_empty()</span><span class='region red'>)</span>)</pre></td></tr><tr><td class='line-number'><a name='L100' href='#L100'><pre>100</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L101' href='#L101'><pre>101</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L102' href='#L102'><pre>102</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for ModifierTrigger {</pre></td></tr><tr><td class='line-number'><a name='L103' href='#L103'><pre>103</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L104' href='#L104'><pre>104</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L105' href='#L105'><pre>105</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        D: Deserializer&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L106' href='#L106'><pre>106</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    {</span></pre></td></tr><tr><td class='line-number'><a name='L107' href='#L107'><pre>107</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        deserializer.deserialize_map(ModifierTriggerVisitor)</span></pre></td></tr><tr><td class='line-number'><a name='L108' href='#L108'><pre>108</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L109' href='#L109'><pre>109</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L110' href='#L110'><pre>110</pre></a></td><td class='skipped-line'></td><td class='code'><pre>struct ModifierTriggerVisitor;</pre></td></tr><tr><td class='line-number'><a name='L111' href='#L111'><pre>111</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for ModifierTriggerVisitor {</pre></td></tr><tr><td class='line-number'><a name='L112' href='#L112'><pre>112</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Value = ModifierTrigger;</pre></td></tr><tr><td class='line-number'><a name='L113' href='#L113'><pre>113</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L114' href='#L114'><pre>114</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L115' href='#L115'><pre>115</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        formatter.write_str(&quot;a struct of CmdPara&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L116' href='#L116'><pre>116</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L117' href='#L117'><pre>117</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L118' href='#L118'><pre>118</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</span></pre></td></tr><tr><td class='line-number'><a name='L119' href='#L119'><pre>119</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    where</span></pre></td></tr><tr><td class='line-number'><a name='L120' href='#L120'><pre>120</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        A: MapAccess&lt;&apos;de&gt;,</span></pre></td></tr><tr><td class='line-number'><a name='L121' href='#L121'><pre>121</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    {</span></pre></td></tr><tr><td class='line-number'><a name='L122' href='#L122'><pre>122</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut operator = None;</span></pre></td></tr><tr><td class='line-number'><a name='L123' href='#L123'><pre>123</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut conditions = None;</span></pre></td></tr><tr><td class='line-number'><a name='L124' href='#L124'><pre>124</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let mut alarms = None</span>;</pre></td></tr><tr><td class='line-number'><a name='L125' href='#L125'><pre>125</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L126' href='#L126'><pre>126</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        while let Some(<span class='region red'>key</span>) = <span class='region red'>map.next_key::&lt;String&gt;()</span><span class='region red'>?</span> {</pre></td></tr><tr><td class='line-number'><a name='L127' href='#L127'><pre>127</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            match <span class='region red'>key.as_str() {</span></pre></td></tr><tr><td class='line-number'><a name='L128' href='#L128'><pre>128</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>                &quot;operator&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L129' href='#L129'><pre>129</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>operator</span> = match <span class='region red'>map.next_value()</span> {</pre></td></tr><tr><td class='line-number'><a name='L130' href='#L130'><pre>130</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Ok(<span class='region red'>val) =&gt; Some(val)</span>,</pre></td></tr><tr><td class='line-number'><a name='L131' href='#L131'><pre>131</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Err(_) =&gt; <span class='region red'>Some(String::new())</span>,</pre></td></tr><tr><td class='line-number'><a name='L132' href='#L132'><pre>132</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L133' href='#L133'><pre>133</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L134' href='#L134'><pre>134</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>&quot;conditions&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L135' href='#L135'><pre>135</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>conditions</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L136' href='#L136'><pre>136</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L137' href='#L137'><pre>137</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                <span class='region red'>&quot;alarms&quot;</span> =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L138' href='#L138'><pre>138</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    <span class='region red'>alarms</span> = <span class='region red'>map.next_value()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L139' href='#L139'><pre>139</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L140' href='#L140'><pre>140</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L141' href='#L141'><pre>141</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    let _ = <span class='region red'>map.next_value::&lt;serde::de::IgnoredAny&gt;()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L142' href='#L142'><pre>142</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L143' href='#L143'><pre>143</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L144' href='#L144'><pre>144</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L145' href='#L145'><pre>145</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>        let <span class='region red'>operator = operator.unwrap_or_else(String::new);</span></pre></td></tr><tr><td class='line-number'><a name='L146' href='#L146'><pre>146</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let conditions = conditions.unwrap_or_else(HashMap::new);</span></pre></td></tr><tr><td class='line-number'><a name='L147' href='#L147'><pre>147</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        let alarms = alarms.unwrap_or_default();</span></pre></td></tr><tr><td class='line-number'><a name='L148' href='#L148'><pre>148</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='red'></span></pre></td></tr><tr><td class='line-number'><a name='L149' href='#L149'><pre>149</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        Ok(ModifierTrigger {</span></pre></td></tr><tr><td class='line-number'><a name='L150' href='#L150'><pre>150</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            operator,</span></pre></td></tr><tr><td class='line-number'><a name='L151' href='#L151'><pre>151</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            conditions: Some(conditions),</span></pre></td></tr><tr><td class='line-number'><a name='L152' href='#L152'><pre>152</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>            alarms,</span></pre></td></tr><tr><td class='line-number'><a name='L153' href='#L153'><pre>153</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        })</span></pre></td></tr><tr><td class='line-number'><a name='L154' href='#L154'><pre>154</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L155' href='#L155'><pre>155</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L156' href='#L156'><pre>156</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L157' href='#L157'><pre>157</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[derive(Debug, Serialize, Clone)]</pre></td></tr><tr><td class='line-number'><a name='L158' href='#L158'><pre>158</pre></a></td><td class='skipped-line'></td><td class='code'><pre>#[serde(rename_all = &quot;camelCase&quot;)]</pre></td></tr><tr><td class='line-number'><a name='L159' href='#L159'><pre>159</pre></a></td><td class='skipped-line'></td><td class='code'><pre>pub struct ModifierAction {</pre></td></tr><tr><td class='line-number'><a name='L160' href='#L160'><pre>160</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub name: String,</pre></td></tr><tr><td class='line-number'><a name='L161' href='#L161'><pre>161</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub rewrite_fields: String,</pre></td></tr><tr><td class='line-number'><a name='L162' href='#L162'><pre>162</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub writable: Option&lt;bool&gt;,</pre></td></tr><tr><td class='line-number'><a name='L163' href='#L163'><pre>163</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub default_value: Option&lt;String&gt;,</pre></td></tr><tr><td class='line-number'><a name='L164' href='#L164'><pre>164</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    pub value_range: Option&lt;ValueRange&gt;,</pre></td></tr><tr><td class='line-number'><a name='L165' href='#L165'><pre>165</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L166' href='#L166'><pre>166</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L167' href='#L167'><pre>167</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl Validator for ModifierAction {</pre></td></tr><tr><td class='line-number'><a name='L168' href='#L168'><pre>168</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn is_valid(&amp;self) -&gt; bool {</span></pre></td></tr><tr><td class='line-number'><a name='L169' href='#L169'><pre>169</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        !self.name.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L170' href='#L170'><pre>170</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; <span class='region red'>!self.rewrite_fields.is_empty()</span></pre></td></tr><tr><td class='line-number'><a name='L171' href='#L171'><pre>171</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>            &amp;&amp; ((<span class='region red'>self.rewrite_fields.contains(&apos;D&apos;)</span></pre></td></tr><tr><td class='line-number'><a name='L172' href='#L172'><pre>172</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                &amp;&amp; <span class='region red'>self.default_value.as_ref().map_or(false, </span>|c| <span class='region red'>!c.is_empty()</span><span class='region red'>)</span>)</pre></td></tr><tr><td class='line-number'><a name='L173' href='#L173'><pre>173</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                || (<span class='region red'>self.rewrite_fields.contains(&apos;V&apos;)</span></pre></td></tr><tr><td class='line-number'><a name='L174' href='#L174'><pre>174</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    &amp;&amp; <span class='region red'>self.value_range.as_ref().map_or(false, </span>|c| <span class='region red'>c.is_valid()</span><span class='region red'>)</span>)</pre></td></tr><tr><td class='line-number'><a name='L175' href='#L175'><pre>175</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                || (<span class='region red'>self.rewrite_fields.contains(&apos;W&apos;)</span></pre></td></tr><tr><td class='line-number'><a name='L176' href='#L176'><pre>176</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    &amp;&amp; <span class='region red'>self.writable.as_ref().map_or(false, </span>|_c| <span class='region red'>true</span><span class='region red'>)</span>))</pre></td></tr><tr><td class='line-number'><a name='L177' href='#L177'><pre>177</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>}</span></pre></td></tr><tr><td class='line-number'><a name='L178' href='#L178'><pre>178</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L179' href='#L179'><pre>179</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Deserialize&lt;&apos;de&gt; for ModifierAction {</pre></td></tr><tr><td class='line-number'><a name='L180' href='#L180'><pre>180</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    fn deserialize&lt;D&gt;(deserializer: D) -&gt; Result&lt;Self, D::Error&gt;</pre></td></tr><tr><td class='line-number'><a name='L181' href='#L181'><pre>181</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    where</pre></td></tr><tr><td class='line-number'><a name='L182' href='#L182'><pre>182</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        D: Deserializer&lt;&apos;de&gt;,</pre></td></tr><tr><td class='line-number'><a name='L183' href='#L183'><pre>183</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L184' href='#L184'><pre>184</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        deserializer.deserialize_map(ModifierActionVisitor)</pre></td></tr><tr><td class='line-number'><a name='L185' href='#L185'><pre>185</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L186' href='#L186'><pre>186</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr><tr><td class='line-number'><a name='L187' href='#L187'><pre>187</pre></a></td><td class='skipped-line'></td><td class='code'><pre>struct ModifierActionVisitor;</pre></td></tr><tr><td class='line-number'><a name='L188' href='#L188'><pre>188</pre></a></td><td class='skipped-line'></td><td class='code'><pre>impl&lt;&apos;de&gt; Visitor&lt;&apos;de&gt; for ModifierActionVisitor {</pre></td></tr><tr><td class='line-number'><a name='L189' href='#L189'><pre>189</pre></a></td><td class='skipped-line'></td><td class='code'><pre>    type Value = ModifierAction;</pre></td></tr><tr><td class='line-number'><a name='L190' href='#L190'><pre>190</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L191' href='#L191'><pre>191</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>    <span class='region red'>fn expecting(&amp;self, formatter: &amp;mut fmt::Formatter) -&gt; fmt::Result {</span></pre></td></tr><tr><td class='line-number'><a name='L192' href='#L192'><pre>192</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>        formatter.write_str(&quot;a struct of CmdPara&quot;)</span></pre></td></tr><tr><td class='line-number'><a name='L193' href='#L193'><pre>193</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre><span class='region red'>    }</span></pre></td></tr><tr><td class='line-number'><a name='L194' href='#L194'><pre>194</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L195' href='#L195'><pre>195</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    fn visit_map&lt;A&gt;(self, mut map: A) -&gt; Result&lt;Self::Value, A::Error&gt;</pre></td></tr><tr><td class='line-number'><a name='L196' href='#L196'><pre>196</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    where</pre></td></tr><tr><td class='line-number'><a name='L197' href='#L197'><pre>197</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        A: MapAccess&lt;&apos;de&gt;,</pre></td></tr><tr><td class='line-number'><a name='L198' href='#L198'><pre>198</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    {</pre></td></tr><tr><td class='line-number'><a name='L199' href='#L199'><pre>199</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        let mut name = String::new();</pre></td></tr><tr><td class='line-number'><a name='L200' href='#L200'><pre>200</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        let mut rewrite_fields = String::new();</pre></td></tr><tr><td class='line-number'><a name='L201' href='#L201'><pre>201</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        let mut writable = None;</pre></td></tr><tr><td class='line-number'><a name='L202' href='#L202'><pre>202</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        let mut default_value = None;</pre></td></tr><tr><td class='line-number'><a name='L203' href='#L203'><pre>203</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        let mut value_range = None;</pre></td></tr><tr><td class='line-number'><a name='L204' href='#L204'><pre>204</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L205' href='#L205'><pre>205</pre></a></td><td class='covered-line'><pre>36</pre></td><td class='code'><pre>        while let Some(<div class='tooltip'>key<span class='tooltip-content'>30</span></div>) = map.next_key::&lt;String&gt;()<div class='tooltip'><span class='region red'>?</span><span class='tooltip-content'>0</span></div> {</pre></td></tr><tr><td class='line-number'><a name='L206' href='#L206'><pre>206</pre></a></td><td class='covered-line'><pre>30</pre></td><td class='code'><pre>            match key.as_str() {</pre></td></tr><tr><td class='line-number'><a name='L207' href='#L207'><pre>207</pre></a></td><td class='covered-line'><pre>30</pre></td><td class='code'><pre>                &quot;name&quot; =&gt; <div class='tooltip'>name = map.next_value().unwrap_or_else(<span class='tooltip-content'>6</span></div>|_| <div class='tooltip'><span class='region red'>String::new()</span><span class='tooltip-content'>0</span></div><div class='tooltip'>)<span class='tooltip-content'>6</span></div>,</pre></td></tr><tr><td class='line-number'><a name='L208' href='#L208'><pre>208</pre></a></td><td class='covered-line'><pre>24</pre></td><td class='code'><pre>                &quot;rewriteFields&quot; =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L209' href='#L209'><pre>209</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                    rewrite_fields = map.next_value().unwrap_or_else(|_| <div class='tooltip'><span class='region red'>String::new()</span><span class='tooltip-content'>0</span></div>)</pre></td></tr><tr><td class='line-number'><a name='L210' href='#L210'><pre>210</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L211' href='#L211'><pre>211</pre></a></td><td class='skipped-line'></td><td class='code'><pre></pre></td></tr><tr><td class='line-number'><a name='L212' href='#L212'><pre>212</pre></a></td><td class='covered-line'><pre>18</pre></td><td class='code'><pre>                &quot;writable&quot; =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L213' href='#L213'><pre>213</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                    writable = match map.next_value::&lt;bool&gt;() {</pre></td></tr><tr><td class='line-number'><a name='L214' href='#L214'><pre>214</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                        Ok(val) =&gt; Some(val),</pre></td></tr><tr><td class='line-number'><a name='L215' href='#L215'><pre>215</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Err(_) =&gt; <span class='region red'>None</span>,</pre></td></tr><tr><td class='line-number'><a name='L216' href='#L216'><pre>216</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L217' href='#L217'><pre>217</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L218' href='#L218'><pre>218</pre></a></td><td class='covered-line'><pre>12</pre></td><td class='code'><pre>                &quot;defaultValue&quot; =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L219' href='#L219'><pre>219</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                    default_value = map.next_value()<div class='tooltip'><span class='region red'>?</span><span class='tooltip-content'>0</span></div>;</pre></td></tr><tr><td class='line-number'><a name='L220' href='#L220'><pre>220</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L221' href='#L221'><pre>221</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                &quot;valueRange&quot; =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L222' href='#L222'><pre>222</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                    value_range = match map.next_value::&lt;ValueRange&gt;() {</pre></td></tr><tr><td class='line-number'><a name='L223' href='#L223'><pre>223</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                        Ok(<span class='region red'>val) =&gt; Some(val)</span>,</pre></td></tr><tr><td class='line-number'><a name='L224' href='#L224'><pre>224</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>                        Err(_) =&gt; None,</pre></td></tr><tr><td class='line-number'><a name='L225' href='#L225'><pre>225</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                    }</pre></td></tr><tr><td class='line-number'><a name='L226' href='#L226'><pre>226</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L227' href='#L227'><pre>227</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                _ =&gt; {</pre></td></tr><tr><td class='line-number'><a name='L228' href='#L228'><pre>228</pre></a></td><td class='uncovered-line'><pre>0</pre></td><td class='code'><pre>                    let _ = <span class='region red'>map.next_value::&lt;serde::de::IgnoredAny&gt;()</span><span class='region red'>?</span>;</pre></td></tr><tr><td class='line-number'><a name='L229' href='#L229'><pre>229</pre></a></td><td class='skipped-line'></td><td class='code'><pre>                }</pre></td></tr><tr><td class='line-number'><a name='L230' href='#L230'><pre>230</pre></a></td><td class='skipped-line'></td><td class='code'><pre>            }</pre></td></tr><tr><td class='line-number'><a name='L231' href='#L231'><pre>231</pre></a></td><td class='skipped-line'></td><td class='code'><pre>        }</pre></td></tr><tr><td class='line-number'><a name='L232' href='#L232'><pre>232</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        Ok(ModifierAction {</pre></td></tr><tr><td class='line-number'><a name='L233' href='#L233'><pre>233</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>            name,</pre></td></tr><tr><td class='line-number'><a name='L234' href='#L234'><pre>234</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>            rewrite_fields,</pre></td></tr><tr><td class='line-number'><a name='L235' href='#L235'><pre>235</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>            writable,</pre></td></tr><tr><td class='line-number'><a name='L236' href='#L236'><pre>236</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>            default_value,</pre></td></tr><tr><td class='line-number'><a name='L237' href='#L237'><pre>237</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>            value_range,</pre></td></tr><tr><td class='line-number'><a name='L238' href='#L238'><pre>238</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>        })</pre></td></tr><tr><td class='line-number'><a name='L239' href='#L239'><pre>239</pre></a></td><td class='covered-line'><pre>6</pre></td><td class='code'><pre>    }</pre></td></tr><tr><td class='line-number'><a name='L240' href='#L240'><pre>240</pre></a></td><td class='skipped-line'></td><td class='code'><pre>}</pre></td></tr></table></div></body></html>