# 覆盖率分析模板使用示例

## 📋 完整使用流程演示

本文档演示如何使用覆盖率分析模板体系来生成项目的覆盖率分析报告。

## 🎯 步骤1: 准备工作

### 1.1 安装必要工具
```bash
# 安装 cargo-llvm-cov
cargo install cargo-llvm-cov

# 验证安装
cargo llvm-cov --version
```

### 1.2 进入项目目录
```bash
cd updevice_rust/rust_updevice
```

## 🔧 步骤2: 执行覆盖率测试

### 2.1 清理之前的数据
```bash
cargo llvm-cov clean
```

### 2.2 生成覆盖率报告
```bash
# 生成 HTML 报告
cargo llvm-cov --test cucumber --html

# 生成文本摘要
cargo llvm-cov --test cucumber --summary-only
```

### 2.3 筛选项目特定数据
```bash
# 只获取 updevice 项目的覆盖率数据
cargo llvm-cov --test cucumber --summary-only | grep "updevice_rust/rust_updevice/src"
```

## 🤖 步骤3: 使用 AI 生成分析报告

### 3.1 选择合适的提示词模板

从 `覆盖率分析报告生成提示词模板.md` 中选择 UPDevice 项目的示例提示词，发送给 AI 助手。

### 3.2 AI 执行流程

AI 会自动：
1. 执行覆盖率测试命令
2. 分析覆盖率数据
3. 生成结构化的分析报告
4. 保存到指定位置

## 📊 步骤4: 查看和分析结果

### 4.1 查看 HTML 报告
```bash
# 在浏览器中打开详细的 HTML 报告
open tests/docs/coverage_html/html/index.html
```

### 4.2 查看生成的分析报告
```bash
# 查看 AI 生成的分析报告
cat tests/docs/Updevice单测覆盖率分析报告.md
```

## 🎯 步骤5: 根据报告改进测试

### 5.1 识别低覆盖率模块

根据报告中的分析，重点关注：
- 数据源模块 (0%-2.67% 覆盖率)
- 设备状态模块 (23%-36% 覆盖率)
- FFI 自动生成代码 (0% 覆盖率)

### 5.2 制定改进计划

**短期目标**:
- 为数据源模块添加单元测试
- 完善设备状态模块的测试用例

**中期目标**:
- 提升核心业务模块覆盖率
- 增加边界条件和异常处理测试

**长期目标**:
- 补充工具类模块测试
- 增加集成测试和端到端测试

## 📈 最佳实践总结

1. **定期执行**: 每次代码提交后都运行覆盖率分析
2. **渐进改进**: 设置阶段性的覆盖率目标
3. **重点突破**: 优先提升低覆盖率的关键模块
4. **团队协作**: 将覆盖率报告分享给团队成员
5. **持续优化**: 根据项目发展调整模板和配置

## 🎉 预期效果

使用这套模板体系，您可以：
- **快速生成**: 5分钟内生成完整的覆盖率分析报告
- **标准化流程**: 确保不同项目的报告格式一致
- **数据准确**: 避免手动统计的错误
- **持续改进**: 基于数据驱动的测试优化
- **团队协作**: 提供清晰的改进方向和优先级
